<?php

namespace App\Jobs;

use App\Models\StoryboardNarration;
use App\Models\ProjectStoryboard;
use App\Services\PyApi\VoiceService;
use App\Services\PyApi\WebSocketEventService;
use App\Enums\ApiCodeEnum;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;

/**
 * 异步处理音色试听任务
 * 实现图表中的完整 WebSocket 进度推送流程
 */
class ProcessVoicePreview implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $taskId;
    protected $userId;
    protected $storyboardId;
    protected $previewText;

    /**
     * 任务超时时间（秒）
     */
    public $timeout = 120; // 2分钟

    /**
     * 最大重试次数
     */
    public $tries = 3;

    public function __construct(string $taskId, int $userId, int $storyboardId, ?string $previewText = null)
    {
        $this->taskId = $taskId;
        $this->userId = $userId;
        $this->storyboardId = $storyboardId;
        $this->previewText = $previewText;
    }

    /**
     * 执行任务
     */
    public function handle()
    {
        $webSocketEventService = app(WebSocketEventService::class);
        $voiceService = app(VoiceService::class);

        try {
            DB::beginTransaction();

            // 推送任务开始进度
            $webSocketEventService->pushAiGenerationProgress(
                $this->taskId,
                $this->userId,
                10,
                "开始音色试听任务"
            );

            // 获取分镜和旁白配置
            $storyboard = ProjectStoryboard::with('project')->find($this->storyboardId);
            if (!$storyboard) {
                throw new \Exception('分镜不存在');
            }

            if ($storyboard->project->user_id !== $this->userId) {
                throw new \Exception('无权访问该分镜');
            }

            $narration = StoryboardNarration::byStoryboard($this->storyboardId)->active()->first();
            if (!$narration || !$narration->hasVoiceConfigured()) {
                throw new \Exception('请先配置音色后再试听');
            }

            // 推送进度更新
            $webSocketEventService->pushAiGenerationProgress(
                $this->taskId,
                $this->userId,
                30,
                "准备音色配置"
            );

            // 确定试听文本
            $previewText = $this->previewText ?? $narration->getEffectiveSubtitleText();
            if (empty($previewText)) {
                throw new \Exception('无可用的旁白文本进行试听');
            }

            // 推送进度更新
            $webSocketEventService->pushAiGenerationProgress(
                $this->taskId,
                $this->userId,
                50,
                "连接AI平台生成音频"
            );

            // 构建音色配置
            $voiceConfig = [
                'voice_id' => $narration->voice_id,
                'platform' => $narration->platform,
                'language' => $narration->language,
                'emotion' => $narration->emotion,
                'speed' => $narration->speed,
                'pitch' => $narration->pitch
            ];

            // 调用VoiceService生成试听音频
            $result = $voiceService->generateVoicePreview(
                $this->userId,
                $voiceConfig,
                $previewText,
                $storyboard->project_id
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                // 生成成功
                $webSocketEventService->pushAiGenerationProgress(
                    $this->taskId,
                    $this->userId,
                    80,
                    "保存试听音频"
                );

                // 更新最后试听URL
                $narration->last_preview_url = $result['data']['audio_url'];
                $narration->save();

                // 推送完成进度
                $webSocketEventService->pushAiGenerationProgress(
                    $this->taskId,
                    $this->userId,
                    100,
                    "音色试听完成"
                );

                // 推送任务完成事件
                $webSocketEventService->pushAiGenerationCompleted(
                    $this->taskId,
                    $this->userId
                );

                DB::commit();

                Log::info('音色试听任务完成', [
                    'task_id' => $this->taskId,
                    'user_id' => $this->userId,
                    'storyboard_id' => $this->storyboardId,
                    'platform' => $voiceConfig['platform'],
                    'cost' => $result['data']['cost'] ?? 0
                ]);

            } else {
                // 生成失败
                throw new \Exception($result['message']);
            }

        } catch (\Exception $e) {
            DB::rollBack();

            // 推送任务失败事件
            $webSocketEventService->pushAiGenerationFailed(
                $this->taskId,
                $this->userId,
                $e->getMessage()
            );

            // 发布失败事件到事件总线
            $this->publishFailureEvent($e->getMessage());

            Log::error('音色试听任务失败', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'storyboard_id' => $this->storyboardId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * 任务失败处理
     */
    public function failed(\Throwable $exception)
    {
        $webSocketEventService = app(WebSocketEventService::class);

        // 推送任务失败事件
        $webSocketEventService->pushAiGenerationFailed(
            $this->taskId,
            $this->userId,
            $exception->getMessage()
        );

        // 发布最终失败事件
        $this->publishFailureEvent($exception->getMessage());

        Log::error('音色试听任务最终失败', [
            'task_id' => $this->taskId,
            'user_id' => $this->userId,
            'storyboard_id' => $this->storyboardId,
            'error' => $exception->getMessage()
        ]);
    }

    /**
     * 发布失败事件到事件总线
     */
    private function publishFailureEvent(string $errorMessage): void
    {
        try {
            // 调用事件发布API
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . config('app.internal_api_token', 'internal_service_token'),
                'Content-Type' => 'application/json'
            ])->post(config('app.url') . '/py-api/events/publish', [
                'event_type' => 'voice_audition_failed',
                'business_id' => $this->taskId,
                'user_id' => $this->userId,
                'error_details' => [
                    'error_message' => $errorMessage,
                    'task_id' => $this->taskId,
                    'storyboard_id' => $this->storyboardId,
                    'preview_text' => $this->previewText
                ],
                'metadata' => [
                    'storyboard_id' => $this->storyboardId,
                    'preview_text' => $this->previewText,
                    'failed_at' => now()->toISOString()
                ]
            ]);

            if ($response->successful()) {
                Log::info('音色试听失败事件发布成功', [
                    'task_id' => $this->taskId,
                    'event_response' => $response->json()
                ]);
            } else {
                Log::warning('音色试听失败事件发布失败', [
                    'task_id' => $this->taskId,
                    'response_status' => $response->status(),
                    'response_body' => $response->body()
                ]);
            }

        } catch (\Exception $e) {
            Log::error('发布音色试听失败事件异常', [
                'task_id' => $this->taskId,
                'error' => $e->getMessage()
            ]);
        }
    }
}
