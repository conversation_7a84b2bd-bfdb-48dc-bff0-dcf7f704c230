<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Py视频创作工具业务流程D-1: 视频创作项目创建流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .description {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .features {
            background-color: #e8f5e9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            border-left: 4px solid #4caf50;
        }
        .features h3 {
            margin-top: 0;
            color: #2e7d32;
        }
        .features ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .features li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.close()">← 返回总览</button>
    <div class="container">
        <h1>🎬 Py视频创作工具业务流程D-1: 视频创作项目创建流程（纯文本数据处理版）</h1>

        <div class="description">
            <strong>流程说明：</strong>本图表展示了Py视频创作工具中"视频创作项目创建"功能的标准化业务流程，支持双模式故事创建（自有故事分镜拆解+智能故事生成）和智能平台选择机制。引用C系列图表（C-1到C-8）进行了全面优化，包括标准化积分处理、统一AI平台选择架构（通过 AiServiceClient 调用）、错误处理、资源管理等机制，确保与系统其他流程的规范一致性。本流程仅处理文本数据，严禁储存或中转任何资源文件。
        </div>

        <div class="mermaid">
sequenceDiagram
    participant U as 用户
    participant F as Py视频创作工具前端
    participant W as WebSocket服务
    participant A as 工具API接口服务

    participant RM as AI资源管理服务
    participant AI as AI平台
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线
    participant CB as 角色绑定界面

    Note over A: 重要：本流程仅处理文本数据，严禁储存或中转任何资源文件

    Note over U: 用户进入项目创建页面

    U->>F: 访问视频创作页面
    F->>A: API请求

    Note over A: 🔐 Token验证流程
    Note over A: 引用 diagram-22-python-token-validation.html
    A->>A: 解析Token

    alt Token验证失败
        A->>F: 返回认证失败
        F->>U: 跳转到登录页面
    else Token验证通过
        A->>DB: 查询用户状态
        F->>A: GET /py-api/projects/styles
        A->>DB: 查询项目风格
        DB->>A: 返回风格列表
        A->>F: 返回风格数据
        F->>U: 渲染创建对话框
    end

    Note over U: 📝 故事创建模式选择
    F->>U: 显示故事模式选择界面

    alt 用户选择"自有故事"模式
        U->>F: 选择"使用我的故事"
        F->>U: 显示故事输入框
        U->>F: 输入自己的故事内容
        Note over F: 用户将在后续点击"拆解分镜"按钮
    else 用户选择"智能故事"模式
        U->>F: 选择"智能故事"
        F->>U: 显示故事要求输入框
        U->>F: 输入故事生成要求/提示
        Note over F: 系统将先生成故事再进行分镜拆解
    end

    U->>F: 输入项目信息(风格选择等)

    Note over F: 🤖 智能平台选择流程
    F->>A: POST /py-api/ai-models/select-platform<br/>business_type=story, auto_recommend=true
    A->>A: 调用智能平台选择服务
    A->>A: 调用AiServiceClient<br/>查询用户历史偏好和使用记录<br/>分析用户偏好+平台状态+任务特性
    A->>F: 返回最佳推荐+备选方案
    A->>F: 返回：推荐"DeepSeek(质量最佳)"<br/>+ 备选[MiniMax]

    Note over F: 🚀 用户体验优化
    alt 用户满意推荐
        F->>U: 直接点击"开始创作"<br/>使用推荐的DeepSeek
    else 用户需要更多选择
        F->>U: 点击"选择其他平台"<br/>从备选方案中选择MiniMax
    end

    Note over F: 🔗 建立WebSocket连接
    F->>W: POST /py-api/websocket/auth<br/>认证WebSocket连接<br/>{<br/>  "client_type": "python_tool",<br/>  "user_token": "Bearer xxx",<br/>  "business_type": "project_creation"<br/>}
    W->>W: 验证用户Token和客户端类型
    W->>DB: 创建WebSocket会话记录
    W->>F: 返回WebSocket连接信息<br/>{<br/>  code: 200,<br/>  message: "WebSocket认证成功",<br/>  data: {<br/>    session_id: "ws_session_xxx",<br/>    websocket_url: "wss://api.tiptop.cn:8080",<br/>    expires_at: "2024-01-01T12:00:00Z"<br/>  }<br/>}
    F->>W: 连接到WebSocket服务器<br/>wss://api.tiptop.cn:8080?session_id=ws_session_xxx
    W->>F: WebSocket连接确认，准备接收进度推送

    alt 自有故事模式处理
        Note over U: 📖 自有故事分镜拆解流程
        U->>F: 点击"拆解分镜"按钮

        F->>W: 故事分镜拆解请求
        W->>A: 转发分镜拆解请求

        A->>W: 推送进度更新(20%, "开始故事分析")
        W->>F: 实时推送进度到前端

        A->>A: 调用StoryService
        A->>A: 调用AiGenerationService
        A->>A: 调用AiServiceClient
        A->>AI: 调用AI平台进行故事分镜拆解
        AI->>A: 返回故事标题和分镜结果

        A->>W: 推送进度更新(80%, "分镜拆解完成")
        W->>F: 实时推送进度到前端


        A->>W: 返回故事标题和分镜数据
        W->>F: 推送分镜拆解成功结果
        F->>U: 显示故事标题和分镜列表(可编辑)

        Note over U: 用户可以合并、修改、删除分镜，或清除项目
        U->>F: 确认分镜内容后点击"创建项目"

    else 智能故事模式处理
        Note over U: 🤖 智能故事生成+分镜拆解流程
        U->>F: 点击"创建项目"按钮

        F->>W: 智能故事生成请求
        W->>A: 转发故事生成请求

        A->>W: 推送进度更新(10%, "开始故事生成")
        W->>F: 实时推送进度到前端

        A->>A: 调用StoryService
        A->>A: 调用AiGenerationService
        A->>A: 调用AiServiceClient
        A->>AI: 调用AI平台生成完整故事
        AI->>A: 返回生成的故事内容

        A->>W: 推送进度更新(50%, "故事生成完成，开始分镜拆解")
        W->>F: 实时推送进度到前端

        A->>A: 调用StoryService
        A->>A: 调用AiGenerationService
        A->>A: 调用AiServiceClient
        A->>AI: 调用AI平台进行分镜拆解
        AI->>A: 返回分镜数据

        A->>W: 推送进度更新(80%, "分镜拆解完成")
        W->>F: 实时推送进度到前端

        A->>W: 返回完整的故事和分镜数据
        W->>F: 推送生成成功结果
        F->>U: 显示生成的故事和分镜列表
    end

    Note over F: 📊 项目创建最终处理
    F->>W: 最终项目创建请求(包含确认的故事和分镜)
    W->>A: 转发项目创建请求

    Note over A: 📊 标准化积分处理流程
    A->>DB: 检查用户积分(事务锁定)

    alt 积分不足
        Note over A: 积分 < 所需积分
        A->>W: 返回积分不足详细信息
        W->>F: 推送积分不足消息(包含充值建议)
        F->>U: 显示积分不足提示
        Note over A: 无扣费操作，保护用户资金
    else 积分充足
        A->>DB: 扣取积分(冻结状态)
        A->>R: 同步积分状态(缓存更新)
        A->>DB: 写入业务日志(状态:冻结)
        A->>R: 缓存业务日志
        A->>RM: 创建项目资源记录

        Note over A: 📈 实时进度推送：开始最终项目创建
        A->>W: 推送进度更新(90%, "创建项目记录")
        W->>F: 实时推送进度到前端

        Note over A: 🎯 保存项目数据到数据库
        A->>DB: 创建项目记录(包含故事和分镜数据)
        DB->>A: 返回项目ID

        A->>RM: 更新资源信息

        alt 项目创建失败
            A->>W: 返回失败结果
            W->>F: 推送失败结果(详细错误信息)
            A->>DB: 更新业务日志(状态:失败)
            A->>DB: 退还积分(解冻→退还)
            A->>R: 同步退还状态
            A->>E: POST /py-api/events/publish<br/>发布失败事件(异步处理)<br/>{<br/>  "event_type": "project_creation_failed",<br/>  "business_id": "project_task_id",<br/>  "user_id": user_id,<br/>  "error_details": {<br/>    "creation_mode": "intelligent",<br/>    "error_code": "AI_SERVICE_ERROR",<br/>    "error_message": "项目创建失败"<br/>  },<br/>  "metadata": {<br/>    "story_content": "...",<br/>    "creation_params": {...},<br/>    "platform": "deepseek"<br/>  }<br/>}
            F->>U: 显示项目创建失败提示
            Note over A: 业务失败，积分已退还
        else 项目创建成功
            A->>DB: 更新业务日志(状态:成功)
            A->>DB: 确认积分扣取(解冻→已扣)
            A->>R: 同步最终状态

            A->>W: 推送进度更新(100%, "项目创建完成")
            W->>F: 实时推送最终完成状态

            Note over A: 🎭 故事角色提取流程
            A->>W: 推送进度更新(85%, "开始提取故事角色")
            W->>F: 实时推送进度到前端

            A->>SC: 调用故事角色提取服务(使用预选平台)
            SC->>AI: 分析故事内容提取角色信息
            AI->>SC: 返回角色列表(动态数量)
            SC->>A: 返回角色提取结果

            A->>W: 推送进度更新(95%, "保存角色信息")
            W->>F: 实时推送进度到前端

            A->>DB: 保存提取的角色信息到project_characters表
            DB->>A: 角色信息保存成功

            A->>W: 返回项目创建成功结果
            W->>F: 推送项目创建成功结果

            Note over A: 📊 用户偏好学习与优化
            A->>DB: 记录用户平台选择行为<br/>(智能推荐/选择理由/故事生成任务/生成质量)
            A->>DB: 更新用户偏好权重<br/>(质量优先/速度优先/成本优先)
            A->>R: 更新用户常用平台缓存
            A->>R: 刷新推荐算法缓存<br/>为下次推荐优化准备数据
        end
    end

    Note over F: 🔚 关闭WebSocket连接
    F->>W: 关闭WebSocket连接

    Note over F: 🎯 跳转到下一个业务流程
    F->>F: 跳转到角色绑定页面
    F->>CB: 传递项目创建结果<br/>{<br/>  code: 200,<br/>  message: "项目创建成功",<br/>  data: {<br/>    project_id, next_step: "character_binding"<br/>  },<br/>  timestamp, request_id<br/>}

    Note over CB: 项目创建流程完成，角色绑定界面接收项目数据并开始角色绑定流程
        </div>

        <div class="features">
            <h3>🎯 图表中体现的标准化流程特性</h3>
            <ul>
                <li><strong>📖 双模式故事创建：</strong>支持"自有故事"（用户输入故事内容进行分镜拆解）和"智能故事"（AI生成完整故事）两种创建模式，满足不同用户需求</li>
                <li><strong>🎬 故事分镜拆解流程：</strong>支持对故事进行智能分镜拆解，生成可编辑的分镜列表，用户可合并、修改、删除分镜</li>
                <li><strong>🔧 统一平台架构：</strong>所有平台选择通过AiServiceClient统一管理，支持deepseek/minimax等多平台</li>
                <li><strong>🔒 标准化积分处理（引用C-2、C-3、C-4）：</strong>事务锁定检查用户积分、积分冻结状态扣取、失败时事务保证返还积分</li>
                <li><strong>⚠️ 错误处理机制（引用C-4）：</strong>事件总线异步处理，发布失败事件进行标准化失败处理</li>
                <li><strong>📦 资源管理标准化（引用C-6）：</strong>AI资源管理服务创建和更新项目资源记录</li>
                <li><strong>📝 纯文本数据处理：</strong>本流程仅处理故事文本和分镜数据，严禁储存或中转任何资源文件</li>
                <li><strong>🔄 环境切换统一（引用C-9）：</strong>所有AI调用通过AiServiceClient，支持环境切换机制</li>
                <li><strong>📊 实时进度推送：</strong>通过WebSocket服务实时推送故事生成、分镜拆解、项目创建等各阶段进度</li>
                <li><strong>🎯 用户偏好学习：</strong>记录用户平台选择行为和偏好权重，为后续智能推荐优化准备数据</li>
                <li><strong>🔐 标准化Token验证：</strong>复用diagram-22-python-token-validation.html标准Token验证流程</li>
                <li><strong>💾 数据库操作：</strong>创建项目记录，包含故事和分镜数据，缓存项目信息到Redis</li>
            </ul>

            <h3>📚 C系列图表引用说明</h3>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                <p><strong>图表中的引用标注说明：</strong></p>
                <ul>
                    <li><strong>C-1：</strong>AI任务调度 - 智能平台选择机制</li>
                    <li><strong>C-2：</strong>AI生成成功 - 积分确认扣取流程</li>
                    <li><strong>C-3：</strong>积分不足 - 快速验证机制</li>
                    <li><strong>C-4：</strong>AI生成失败 - 事件总线异步处理</li>
                    <li><strong>C-6：</strong>资源管理 - AI资源管理服务</li>
                    <li><strong>C-7：</strong>资源下载 - 直接下载机制</li>
                    <li><strong>C-8：</strong>作品发布 - 可选发布流程</li>
                    <li><strong>C-9：</strong>环境切换 - AiServiceClient统一调用</li>
                </ul>
                <p><em>这些引用标注帮助开发者理解当前流程与标准化规范的对应关系，确保实现的一致性。</em></p>
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
