<?php

namespace App\Services\PyApi;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Models\AiModelConfig;
use App\Models\AiGenerationTask;
use App\Models\CharacterLibrary;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * 图像生成服务
 * 第2D1阶段：图像生成模块
 */
class ImageService
{
    protected $aiModelService;
    protected $pointsService;

    public function __construct(AiModelService $aiModelService, PointsService $pointsService)
    {
        $this->aiModelService = $aiModelService;
        $this->pointsService = $pointsService;
    }

    /**
     * 生成图像
     */
    public function generateImage(int $userId, string $prompt, ?int $characterId = null, ?int $projectId = null, array $generationParams = []): array
    {
        try {
            // 获取角色信息
            $character = null;
            if ($characterId) {
                $character = CharacterLibrary::find($characterId);
                if (!$character) {
                    return [
                        'code' => ApiCodeEnum::NOT_FOUND,
                        'message' => '角色不存在',
                        'data' => []
                    ];
                }
            }

            // 验证用户选择的平台
            $platform = $generationParams['platform'] ?? 'liblib';
            $taskType = 'image_generation';

            // 构建增强提示词
            $enhancedPrompt = $this->buildImagePrompt($prompt, $character, $generationParams);

            // 计算预估成本（简化版本）
            $estimatedCost = $this->calculateSimpleImageCost($generationParams);

            // 🔒 事务锁定积分流程
            // 1. 检查用户积分
            $pointsCheck = $this->pointsService->checkPoints($userId, $estimatedCost, 'image_generation');
            if ($pointsCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return $pointsCheck;
            }

            // 2. 检查积分是否充足
            if (!$pointsCheck['data']['sufficient']) {
                return [
                    'code' => ApiCodeEnum::INSUFFICIENT_POINTS,
                    'message' => '积分不足',
                    'data' => [
                        'required' => $estimatedCost,
                        'available' => $pointsCheck['data']['current_balance'],
                        'shortage' => $pointsCheck['data']['shortage']
                    ]
                ];
            }

            // 3. 冻结积分（事务锁定）
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'image_generation',
                null, // business_id 暂时为空，生成成功后会更新
                600   // 10分钟超时
            );

            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $freezeResult;
            }

            $freezeId = $freezeResult['data']['freeze_id'] ?? null;

            // 构建AI服务请求数据
            $requestData = [
                'prompt' => $enhancedPrompt,
                'aspect_ratio' => $generationParams['aspect_ratio'] ?? '16:9',
                'quality' => $generationParams['quality'] ?? 'standard',
                'style' => $generationParams['style'] ?? null,
                'width' => $this->getWidthFromAspectRatio($generationParams['aspect_ratio'] ?? '16:9'),
                'height' => $this->getHeightFromAspectRatio($generationParams['aspect_ratio'] ?? '16:9')
            ];

            // 直接调用 AiServiceClient::callWithUserChoice
            $response = \App\Services\AiServiceClient::callWithUserChoice(
                $platform,
                $taskType,
                $requestData,
                $userId
            );

            if ($response['success']) {
                // 4. 确认积分扣取（解冻→已扣）
                if ($freezeId) {
                    $confirmResult = $this->pointsService->confirmPointsUsage(
                        $userId,
                        $estimatedCost,
                        'image_generation',
                        0 // 暂时使用0作为business_id，实际应该是生成的资源ID
                    );

                    if ($confirmResult['code'] !== ApiCodeEnum::SUCCESS) {
                        Log::warning('积分确认扣取失败，但图片生成成功', [
                            'user_id' => $userId,
                            'freeze_id' => $freezeId,
                            'cost' => $estimatedCost,
                            'error' => $confirmResult['message'] ?? '未知错误'
                        ]);
                    }
                }

                // 创建资源记录（如果需要）
                $resourceId = null;
                if (isset($response['data']['data']['image_url'])) {
                    $resourceId = $this->createImageResource(
                        $userId,
                        $projectId,
                        $response['data']['data'],
                        $generationParams
                    );
                }

                Log::info('图像生成成功', [
                    'user_id' => $userId,
                    'platform' => $platform,
                    'cost' => $estimatedCost,
                    'resource_id' => $resourceId
                ]);

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '图像生成成功',
                    'data' => [
                        'image_url' => $response['data']['data']['image_url'] ?? '',
                        'thumbnail_url' => $response['data']['data']['thumbnail_url'] ?? '',
                        'resource_id' => $resourceId,
                        'cost' => $estimatedCost,
                        'platform' => $platform,
                        'metadata' => [
                            'width' => $response['data']['data']['width'] ?? 1024,
                            'height' => $response['data']['data']['height'] ?? 1024,
                            'format' => 'jpg',
                            'file_size' => $response['data']['data']['file_size'] ?? '2.5MB'
                        ]
                    ]
                ];
            } else {
                // 5. AI服务调用失败，退还积分（解冻→退还）
                if ($freezeId) {
                    $refundResult = $this->pointsService->refundPointsByFreezeId(
                        $userId,
                        $freezeId,
                        'AI服务调用失败，自动退还积分'
                    );
                    if ($refundResult['code'] !== ApiCodeEnum::SUCCESS) {
                        Log::error('积分退还失败', [
                            'user_id' => $userId,
                            'freeze_id' => $freezeId,
                            'cost' => $estimatedCost,
                            'error' => $refundResult['message'] ?? '未知错误'
                        ]);
                    }
                }

                return [
                    'code' => ApiCodeEnum::AI_SERVICE_ERROR,
                    'message' => 'AI服务调用失败：' . ($response['error'] ?? '未知错误'),
                    'data' => [
                        'platform' => $platform,
                        'error_code' => $response['code'] ?? 'UNKNOWN_ERROR'
                    ]
                ];
            }

        } catch (\Exception $e) {
            // 6. 异常情况下，退还积分（如果已冻结）
            if (isset($freezeId) && $freezeId) {
                $refundResult = $this->pointsService->refundPointsByFreezeId(
                    $userId,
                    $freezeId,
                    '图像生成异常，自动退还积分'
                );
                if ($refundResult['code'] !== ApiCodeEnum::SUCCESS) {
                    Log::error('异常情况下积分退还失败', [
                        'user_id' => $userId,
                        'freeze_id' => $freezeId,
                        'cost' => $estimatedCost ?? 0,
                        'error' => $refundResult['message'] ?? '未知错误'
                    ]);
                }
            }

            $error_context = [
                'user_id' => $userId,
                'prompt' => substr($prompt, 0, 100),
                'character_id' => $characterId,
                'project_id' => $projectId,
                'generation_params_count' => is_array($generationParams) ? count($generationParams) : 0,
            ];

            Log::error('图像生成失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '图像生成失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 计算简化的图像生成成本
     */
    private function calculateSimpleImageCost(array $params): float
    {
        $baseCost = 10.0; // 基础成本

        // 根据质量调整成本
        $quality = $params['quality'] ?? 'standard';
        switch ($quality) {
            case 'hd':
                $baseCost *= 1.5;
                break;
            case 'ultra':
                $baseCost *= 2.0;
                break;
            default:
                // standard 保持基础成本
                break;
        }

        return $baseCost;
    }

    /**
     * 根据宽高比获取宽度
     */
    private function getWidthFromAspectRatio(string $aspectRatio): int
    {
        switch ($aspectRatio) {
            case '16:9':
                return 1024;
            case '9:16':
                return 576;
            case '1:1':
                return 1024;
            case '4:3':
                return 1024;
            case '3:4':
                return 768;
            default:
                return 1024;
        }
    }

    /**
     * 根据宽高比获取高度
     */
    private function getHeightFromAspectRatio(string $aspectRatio): int
    {
        switch ($aspectRatio) {
            case '16:9':
                return 576;
            case '9:16':
                return 1024;
            case '1:1':
                return 1024;
            case '4:3':
                return 768;
            case '3:4':
                return 1024;
            default:
                return 1024;
        }
    }

    /**
     * 创建图像资源记录
     */
    private function createImageResource(int $userId, ?int $projectId, array $imageData, array $generationParams): ?int
    {
        try {
            // 这里应该创建 Resource 记录，但为了简化，暂时返回 null
            // 实际实现中应该调用 ResourceService 来创建资源记录
            return null;
        } catch (\Exception $e) {
            Log::error('创建图像资源记录失败', [
                'user_id' => $userId,
                'project_id' => $projectId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取图像生成状态
     */
    public function getImageStatus(int $taskId, int $userId): array
    {
        try {
            // 原服务层的业务代码逻辑
            $task = AiGenerationTask::where('id', $taskId)
                ->where('user_id', $userId)
                ->where('task_type', AiGenerationTask::TYPE_IMAGE_GENERATION)
                ->first();

            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            $data = [
                'id' => $task->id,
                'task_type' => $task->task_type,
                'status' => $task->status,
                'platform' => $task->platform,
                'cost' => $task->cost,
                'processing_time_ms' => $task->processing_time_ms,
                'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                'completed_at' => $task->completed_at ? $task->completed_at->format('Y-m-d H:i:s') : null
            ];

            // 如果任务完成，添加生成结果
            if ($task->status === AiGenerationTask::STATUS_COMPLETED && $task->output_data) {
                $data['image_url'] = $task->output_data['image_url'] ?? '';
                $data['thumbnail_url'] = $task->output_data['thumbnail_url'] ?? '';
            }

            // 如果任务失败，添加错误信息
            if ($task->status === AiGenerationTask::STATUS_FAILED) {
                $data['error_message'] = $task->error_message;
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('获取图像状态失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取图像状态失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取图像生成结果
     */
    public function getImageResult(int $taskId, int $userId): array
    {
        try {
            // 原服务层的业务代码逻辑
            $task = AiGenerationTask::where('id', $taskId)
                ->where('user_id', $userId)
                ->where('task_type', AiGenerationTask::TYPE_IMAGE_GENERATION)
                ->first();

            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            if ($task->status !== AiGenerationTask::STATUS_COMPLETED) {
                return [
                    'code' => ApiCodeEnum::FAIL,
                    'message' => '任务尚未完成',
                    'data' => []
                ];
            }

            $data = [
                'task_id' => $task->id,
                'image_url' => $task->output_data['image_url'] ?? '',
                'thumbnail_url' => $task->output_data['thumbnail_url'] ?? '',
                'metadata' => $task->output_data['metadata'] ?? [],
                'download_info' => [
                    'direct_url' => $task->output_data['image_url'] ?? '',
                    'expires_at' => Carbon::now()->addDays(7)->format('Y-m-d H:i:s')
                ]
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('获取图像结果失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取图像结果失败',
                'data' => null
            ];
        }
    }

    /**
     * 批量生成图像
     */
    public function batchGenerateImages(int $userId, array $prompts, ?int $projectId = null, array $commonParams = []): array
    {
        try {
            DB::beginTransaction();

            $batchId = 'batch_' . time() . '_' . $userId;
            $taskIds = [];
            $totalCost = 0;

            foreach ($prompts as $prompt) {
                $result = $this->generateImage($userId, $prompt, null, $projectId, $commonParams);
                
                if ($result['code'] === ApiCodeEnum::SUCCESS) {
                    $taskIds[] = $result['data']['task_id'];
                    $totalCost += $result['data']['estimated_cost'];
                } else {
                    DB::rollBack();
                    return $result;
                }
            }

            DB::commit();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '批量图像生成任务创建成功',
                'data' => [
                    'batch_id' => $batchId,
                    'task_ids' => $taskIds,
                    'total_count' => count($taskIds),
                    'estimated_cost' => number_format($totalCost, 4)
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'prompts_count' => count($prompts),
                'project_id' => $projectId,
                'common_params_count' => is_array($commonParams) ? count($commonParams) : 0,
            ];

            Log::error('批量图像生成失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '批量图像生成失败',
                'data' => null
            ];
        }
    }

    /**
     * 构建图像提示词
     */
    private function buildImagePrompt(string $prompt, ?CharacterLibrary $character, array $params): string
    {
        $enhancedPrompt = $prompt;

        // 添加角色信息
        if ($character) {
            $enhancedPrompt .= "\n\n角色描述：" . $character->description;
            if ($character->appearance) {
                $enhancedPrompt .= "\n外观特征：" . $character->appearance;
            }
        }

        // 添加风格信息
        if (!empty($params['style'])) {
            $enhancedPrompt .= "\n\n风格要求：" . $params['style'];
        }

        // 添加质量要求
        $quality = $params['quality'] ?? 'standard';
        if ($quality === 'hd') {
            $enhancedPrompt .= "\n\n高清画质，细节丰富，专业摄影";
        }

        return $enhancedPrompt;
    }

    /**
     * 计算图像生成成本
     */
    private function calculateImageCost(AiModelConfig $model, array $params): float
    {
        $baseCost = $model->cost_per_token;
        
        // 质量影响成本
        $quality = $params['quality'] ?? 'standard';
        $qualityMultiplier = $quality === 'hd' ? 2.0 : 1.0;

        return round($baseCost * $qualityMultiplier, 4);
    }

    /**
     * 执行图像生成
     */
    private function executeImageGeneration(AiGenerationTask $task): void
    {
        try {
            $task->update([
                'status' => AiGenerationTask::STATUS_PROCESSING,
                'started_at' => Carbon::now()
            ]);

            // 调用AI服务
            $result = $this->callAiService($task);

            if ($result['success']) {
                $task->update([
                    'status' => AiGenerationTask::STATUS_COMPLETED,
                    'output_data' => $result['data'],
                    'completed_at' => Carbon::now(),
                    'processing_time_ms' => Carbon::now()->diffInMilliseconds($task->started_at)
                ]);

                // 确认积分消费
                $this->pointsService->confirmPointsUsage($task->user_id, $task->cost, 'image_generation', $task->id);
            } else {
                $task->update([
                    'status' => AiGenerationTask::STATUS_FAILED,
                    'error_message' => $result['error'],
                    'completed_at' => Carbon::now()
                ]);

                // 返还积分
                $this->pointsService->refundPoints($task->user_id, $task->cost, 'image_generation_failed', $task->id);
            }

        } catch (\Exception $e) {
            $task->update([
                'status' => AiGenerationTask::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => Carbon::now()
            ]);

            // 返还积分
            $this->pointsService->refundPoints($task->user_id, $task->cost, 'image_generation_error', $task->id);

            Log::error('图像生成执行失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 调用AI服务
     *
     * 🚨 架构边界规范：
     * ✅ 工具API接口服务负责调用AI服务，不进行模拟
     * ✅ 使用 AiServiceClient 实现环境切换
     * ❌ 不在工具API中进行模拟返回数据
     *
     * 🚨 升级：支持用户平台选择和偏好记录
     */
    private function callAiService(AiGenerationTask $task): array
    {
        try {
            $taskType = 'image_generation';
            $requestData = [
                'prompt' => $task->input_data['enhanced_prompt'],
                'aspect_ratio' => $task->input_data['aspect_ratio'],
                'quality' => $task->input_data['quality']
            ];

            // 🚨 升级：使用 callWithUserChoice 方法，支持用户偏好记录
            $response = \App\Services\AiServiceClient::callWithUserChoice(
                $task->platform,
                $taskType,
                $requestData,
                $task->user_id
            );

            if ($response['success']) {
                $data = $response['data'];

                return [
                    'success' => true,
                    'data' => [
                        'image_url' => $data['data']['image_url'] ?? '',
                        'thumbnail_url' => $data['data']['thumbnail_url'] ?? '',
                        'metadata' => [
                            'width' => $data['data']['width'] ?? 1024,
                            'height' => $data['data']['height'] ?? 1024,
                            'format' => 'jpg',
                            'file_size' => $data['data']['file_size'] ?? '2.5MB'
                        ],
                        'mode' => $response['mode'],
                        'user_choice' => $response['user_choice'] ?? null // 🚨 升级：包含用户选择信息
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'AI服务调用失败：' . ($response['error'] ?? '未知错误'),
                    'code' => $response['code'] ?? 'UNKNOWN_ERROR'
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'AI服务调用异常：' . $e->getMessage(),
                'code' => 'EXCEPTION_ERROR'
            ];
        }
    }
}
