<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 系统事件表迁移
 * 用于记录系统内部事件，支持事件驱动架构
 */
class CreateSystemEventsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('system_events', function (Blueprint $table) {
            $table->id();
            $table->string('event_id', 100)->unique()->comment('事件唯一标识');
            $table->string('event_type', 100)->comment('事件类型');
            $table->string('business_id', 100)->comment('业务ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->json('error_details')->nullable()->comment('错误详情（JSON格式）');
            $table->json('metadata')->nullable()->comment('元数据（JSON格式）');
            $table->string('source', 50)->default('api_service')->comment('事件来源');
            $table->string('version', 10)->default('1.0')->comment('事件版本');
            $table->enum('status', ['pending', 'processed', 'failed'])->default('pending')->comment('处理状态');
            $table->timestamp('processed_at')->nullable()->comment('处理时间');
            $table->timestamps();

            // 索引
            $table->index(['event_type', 'created_at'], 'idx_event_type_created');
            $table->index(['business_id', 'event_type'], 'idx_business_event');
            $table->index(['user_id', 'created_at'], 'idx_user_created');
            $table->index(['status', 'created_at'], 'idx_status_created');
            $table->index('event_id', 'idx_event_id');

            $table->comment('系统事件表 - 记录系统内部事件，支持事件驱动架构');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('system_events');
    }
}
