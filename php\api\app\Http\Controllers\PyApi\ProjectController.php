<?php

namespace App\Http\Controllers\PyApi;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Models\Project;
use App\Models\StyleLibrary;
use App\Services\AuthService;
use App\Services\PyApi\ProjectService;
use App\Services\PyApi\WebSocketService;
use App\Services\PyApi\WebSocketEventService;
use App\Jobs\ProcessProjectCreation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Helpers\LogCheckHelper;

/**
 * 项目创建管理与配置
 */
class ProjectController extends Controller
{
    protected $projectService;

    public function __construct(ProjectService $projectService)
    {
        $this->projectService = $projectService;
    }

    /**
     * @ApiTitle(选风格+写剧情创建项目)
     * @ApiSummary(创建新项目，包含防刷机制)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/projects/create-with-story)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="style_id", type="int", required=true, description="风格ID")
     * @ApiParams(name="story_content", type="string", required=true, description="剧情内容")
     * @ApiParams(name="title", type="string", required=false, description="项目标题")
     * @ApiParams(name="description", type="string", required=false, description="项目描述")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.project_id", type="int", required=true, description="项目ID")
     * @ApiReturnParams (name="data.ai_generated_title", type="string", required=false, description="AI生成的标题")
     * @ApiReturnParams (name="data.title_confirmed", type="boolean", required=true, description="标题是否已确认")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "项目创建成功",
     *   "data": {
     *     "project_id": 123,
     *     "ai_generated_title": "AI生成的精彩标题",
     *     "title_confirmed": false
     *   }
     * })
     */
    public function createWithStory(Request $request)
    {
        try {
            $rules = [
                'style_id' => 'required|integer|exists:style_library,id',
                'story_content' => 'required|string|min:10|max:5000',
                'title' => 'sometimes|string|max:200',
                'description' => 'sometimes|string|max:1000'
            ];

            $messages = [
                'style_id.required' => '风格ID不能为空',
                'style_id.exists' => '风格不存在',
                'story_content.required' => '剧情内容不能为空',
                'story_content.min' => '剧情内容至少10个字符',
                'story_content.max' => '剧情内容不能超过5000个字符',
                'title.max' => '标题不能超过200个字符',
                'description.max' => '描述不能超过1000个字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 防刷机制检查
            $antiSpamResult = $this->projectService->checkAntiSpam($user->id);
            if ($antiSpamResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $this->errorResponse(
                    $antiSpamResult['code'],
                    $antiSpamResult['message'],
                    $antiSpamResult['data']
                );
            }

            $result = $this->projectService->createWithStory(
                $user->id,
                $request->style_id,
                $request->story_content,
                $request->title,
                $request->description
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('创建项目失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '创建项目失败', []);
        }
    }

    /**
     * @ApiTitle(确认AI生成的项目标题)
     * @ApiSummary(确认使用AI生成的项目标题)
     * @ApiMethod(PUT)
     * @ApiRoute(/py-api/projects/{id}/confirm-title)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="项目ID")
     * @ApiParams(name="use_ai_title", type="boolean", required=true, description="是否使用AI生成的标题")
     * @ApiParams(name="custom_title", type="string", required=false, description="自定义标题")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.title", type="string", required=true, description="最终确认的标题")
     * @ApiReturnParams (name="data.title_confirmed", type="boolean", required=true, description="标题确认状态")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "标题确认成功",
     *   "data": {
     *     "title": "最终确认的标题",
     *     "title_confirmed": true
     *   }
     * })
     */
    public function confirmTitle(Request $request, $id)
    {
        try {
            $rules = [
                'use_ai_title' => 'required|boolean',
                'custom_title' => 'required_if:use_ai_title,false|string|max:200'
            ];

            $messages = [
                'use_ai_title.required' => '请选择是否使用AI生成的标题',
                'custom_title.required_if' => '不使用AI标题时必须提供自定义标题',
                'custom_title.max' => '自定义标题不能超过200个字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->projectService->confirmTitle(
                $id,
                $user->id,
                $request->use_ai_title,
                $request->custom_title
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('确认项目标题失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '确认项目标题失败', []);
        }
    }

    /**
     * @ApiTitle(获取用户项目列表)
     * @ApiSummary(获取当前用户的项目列表)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/projects/my-projects)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="status", type="string", required=false, description="项目状态筛选")
     * @ApiParams(name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams(name="per_page", type="int", required=false, description="每页数量，默认20")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.projects", type="array", required=true, description="项目列表")
     * @ApiReturnParams (name="data.pagination", type="object", required=true, description="分页信息")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "projects": [
     *       {
     *         "id": 1,
     *         "title": "我的项目",
     *         "status": "draft",
     *         "style_name": "浪漫爱情",
     *         "created_at": "2024-01-01 12:00:00",
     *         "last_accessed_at": "2024-01-01 15:00:00"
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "total": 10,
     *       "per_page": 20
     *     }
     *   }
     * })
     */
    public function myProjects(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $page = $request->get('page', 1);
            $perPage = min($request->get('per_page', 20), 100);

            $query = Project::with('style')->byUser($user->id);

            // 状态筛选
            if ($request->has('status')) {
                $query->byStatus($request->status);
            }

            $projects = $query->orderBy('last_accessed_at', 'desc')
                ->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            $projectsData = $projects->map(function ($project) {
                return [
                    'id' => $project->id,
                    'title' => $project->title,
                    'description' => $project->description,
                    'status' => $project->status,
                    'style_name' => $project->style?->name,
                    'title_confirmed' => $project->title_confirmed,
                    'is_public' => $project->is_public,
                    'view_count' => $project->view_count,
                    'created_at' => $project->created_at->format('Y-m-d H:i:s'),
                    'last_accessed_at' => $project->last_accessed_at?->format('Y-m-d H:i:s'),
                    'completed_at' => $project->completed_at?->format('Y-m-d H:i:s')
                ];
            });

            return $this->successResponse([
                'projects' => $projectsData,
                'pagination' => [
                    'current_page' => $projects->currentPage(),
                    'total' => $projects->total(),
                    'per_page' => $projects->perPage(),
                    'last_page' => $projects->lastPage()
                ]
            ], 'success');
        } catch (\Exception $e) {
            Log::error('获取我的项目列表失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取我的项目列表失败', []);
        }
    }

    /**
     * @ApiTitle(获取项目详情)
     * @ApiSummary(获取指定项目的详细信息)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/projects/{id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="项目ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="项目详情")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 1,
     *     "title": "我的项目",
     *     "description": "项目描述",
     *     "story_content": "剧情内容",
     *     "status": "draft",
     *     "style": {
     *       "id": 1,
     *       "name": "浪漫爱情"
     *     },
     *     "ai_generated_title": "AI生成的标题",
     *     "title_confirmed": false,
     *     "project_config": {},
     *     "metadata": {},
     *     "created_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function detail($id, Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $project = Project::with('style')->byUser($user->id)->find($id);

            if (!$project) {
                return $this->errorResponse(ApiCodeEnum::NOT_FOUND, '项目不存在');
            }

            // 更新访问时间
            $project->updateLastAccessed();

            return $this->successResponse([
                'id' => $project->id,
                'title' => $project->title,
                'description' => $project->description,
                'story_content' => $project->story_content,
                'status' => $project->status,
                'style' => $project->style ? [
                    'id' => $project->style->id,
                    'name' => $project->style->name,
                    'category' => $project->style->category
                ] : null,
                'ai_generated_title' => $project->ai_generated_title,
                'title_confirmed' => $project->title_confirmed,
                'project_config' => $project->project_config ?? [],
                'metadata' => $project->metadata ?? [],
                'is_public' => $project->is_public,
                'view_count' => $project->view_count,
                'created_at' => $project->created_at->format('Y-m-d H:i:s'),
                'last_accessed_at' => $project->last_accessed_at?->format('Y-m-d H:i:s'),
                'completed_at' => $project->completed_at?->format('Y-m-d H:i:s')
            ], 'success');
        } catch (\Exception $e) {
            Log::error('获取项目详情失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取项目详情失败', []);
        }
    }

    /**
     * 获取项目列表 (简化版)
     * 修复500错误 - 添加缺失的list方法
     */
    public function list(Request $request)
    {
        try {
            return $this->myProjects($request);
        } catch (\Exception $e) {
            Log::error('获取项目列表失败', [
                'method' => __METHOD__,
                'user_id' => auth()->id(),
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取项目列表失败', []);
        }
    }

    /**
     * 创建项目 (简化版)
     * 修复500错误 - 添加缺失的create方法
     */
    public function create(Request $request)
    {
        try {
            // 验证用户身份
            $authResult = AuthService::authenticate($request);

            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 验证请求参数
            $this->validateData($request->all(), [
                'title' => 'required|string|max:200', 'description' => 'sometimes|string|max:1000'
            ], []);

            // 创建项目
            $result = $this->projectService->createSimpleProject(
                $user->id,
                $request->input('title'),
                $request->input('description', '')
            );

            return $this->successResponse($result, '项目创建成功');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '参数验证失败', $e->errors());
        } catch (\Exception $e) {
            Log::error('创建项目失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '创建项目失败', []);
        }
    }

    /**
     * 获取项目详情 (简化版)
     * 修复500错误 - 添加缺失的show方法
     */
    public function show($id, Request $request)
    {
        try {
            return $this->detail($id, $request);
        } catch (\Exception $e) {
            Log::error('获取项目详情失败', [
                'method' => __METHOD__,
                'user_id' => auth()->id(),
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取项目详情失败', []);
        }
    }

    /**
     * 更新项目
     * 修复500错误 - 添加缺失的update方法
     */
    public function update($id, Request $request)
    {
        try {
            // 验证用户身份
            $authResult = AuthService::authenticate($request);

            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 验证请求参数
            $this->validateData($request->all(), [
                'title' => 'sometimes|string|max:200', 'description' => 'sometimes|string|max:1000'
            ], []);

            // 更新项目
            $result = $this->projectService->updateProject(
                $id,
                $user->id,
                $request->only(['title', 'description'])
            );

            if (!$result) {
                return $this->errorResponse(ApiCodeEnum::NOT_FOUND, '项目不存在或无权限');
            }

            return $this->successResponse([], '项目更新成功');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '参数验证失败', $e->errors());
        } catch (\Exception $e) {
            Log::error('更新项目失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '更新项目失败', []);
        }
    }

    /**
     * 删除项目
     * 修复500错误 - 添加缺失的delete方法
     */
    public function delete($id, Request $request)
    {
        try {
            // 验证用户身份
            $authResult = AuthService::authenticate($request);

            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 删除项目
            $result = $this->projectService->deleteProject($id, $user->id);

            if (!$result) {
                return $this->errorResponse(ApiCodeEnum::NOT_FOUND, '项目不存在或无权限');
            }

            return $this->successResponse([], '项目删除成功');

        } catch (\Exception $e) {
            Log::error('删除项目失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '删除项目失败', []);
        }
    }

    /**
     * 创建项目（WebSocket版本）
     * @ApiTitle(创建项目-WebSocket版本)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/projects/create-with-websocket)
     * @ApiParams(name="mode", type="string", required=true, description="创建模式：intelligent或custom")
     * @ApiParams(name="title", type="string", required=true, description="项目标题")
     * @ApiParams(name="description", type="string", required=false, description="项目描述")
     * @ApiParams(name="style_id", type="int", required=false, description="风格ID")
     * @ApiParams(name="story_prompt", type="string", required=false, description="故事提示词（智能模式）")
     * @ApiParams(name="story_content", type="string", required=false, description="故事内容（自有故事模式）")
     * @ApiParams(name="generation_params", type="object", required=false, description="生成参数")
     * @ApiParams(name="websocket_session_id", type="string", required=false, description="WebSocket会话ID")
     * @ApiReturnParams(name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams(name="message", type="string", required=true, description="消息")
     * @ApiReturnParams(name="data", type="object", required=true, description="任务信息")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "项目创建任务创建成功",
     *   "data": {
     *     "task_id": "project_123456",
     *     "status": "processing",
     *     "mode": "intelligent",
     *     "estimated_cost": 20.0
     *   }
     * })
     */
    public function createWithWebSocket(Request $request)
    {
        try {
            // 参数验证
            $rules = [
                'mode' => 'required|string|in:intelligent,custom',
                'title' => 'required|string|max:200',
                'description' => 'sometimes|string|max:1000',
                'style_id' => 'sometimes|integer|exists:style_libraries,id',
                'story_prompt' => 'required_if:mode,intelligent|string|max:2000',
                'story_content' => 'required_if:mode,custom|string|max:10000',
                'generation_params' => 'sometimes|array',
                'websocket_session_id' => 'sometimes|string'
            ];

            $messages = [
                'mode.required' => '创建模式不能为空',
                'mode.in' => '创建模式必须是intelligent或custom',
                'title.required' => '项目标题不能为空',
                'title.max' => '项目标题不能超过200字符',
                'description.max' => '项目描述不能超过1000字符',
                'style_id.exists' => '指定的风格不存在',
                'story_prompt.required_if' => '智能模式需要提供故事提示词',
                'story_content.required_if' => '自有故事模式需要提供故事内容',
                'story_prompt.max' => '故事提示词不能超过2000字符',
                'story_content.max' => '故事内容不能超过10000字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $mode = $request->get('mode');
            $webSocketSessionId = $request->get('websocket_session_id');

            // 如果有WebSocket会话ID，验证会话
            if ($webSocketSessionId) {
                $webSocketService = app(WebSocketService::class);
                $sessionValid = $webSocketService->validateSession($webSocketSessionId, $user->id);
                if (!$sessionValid) {
                    return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, 'WebSocket会话无效', []);
                }
            }

            // 构建项目数据
            $projectData = [
                'title' => $request->get('title'),
                'description' => $request->get('description', ''),
                'style_id' => $request->get('style_id'),
                'generation_params' => $request->get('generation_params', [])
            ];

            if ($mode === 'intelligent') {
                $projectData['story_prompt'] = $request->get('story_prompt');
            } else {
                $projectData['story_content'] = $request->get('story_content');
            }

            // 生成任务ID
            $taskId = 'project_' . time() . '_' . Str::random(8);

            // 创建异步任务
            ProcessProjectCreation::dispatch(
                $taskId,
                $user->id,
                $projectData,
                $mode
            );

            // 立即返回任务信息
            $responseData = [
                'task_id' => $taskId,
                'status' => 'processing',
                'mode' => $mode,
                'estimated_cost' => $mode === 'intelligent' ? 20.0 : 10.0,
                'project_data' => $projectData,
                'websocket_session_id' => $webSocketSessionId,
                'timestamp' => now()->toISOString(),
                'request_id' => 'req_' . Str::random(16)
            ];

            Log::info('项目创建任务创建成功', [
                'task_id' => $taskId,
                'user_id' => $user->id,
                'mode' => $mode,
                'title' => $projectData['title']
            ]);

            return $this->successResponse($responseData, '项目创建任务创建成功');

        } catch (\Exception $e) {
            Log::error('创建项目任务失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '创建项目任务失败', []);
        }
    }
}
