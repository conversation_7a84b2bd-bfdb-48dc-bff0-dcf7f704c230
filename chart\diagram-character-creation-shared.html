<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>共享业务流程: 角色创建流程（可复用标准流程）</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .description {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        .features {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .features h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .features ul {
            list-style-type: none;
            padding-left: 0;
        }
        .features li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        .reference-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .reference-box h4 {
            color: #856404;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.location.href='all-diagrams-index.html'">← 返回图表索引</button>

    <div class="container">
        <h1>🔄 共享业务流程: 角色创建流程（可复用标准流程）</h1>

        <div class="description">
            <strong>流程说明：</strong>这是一个独立的、可复用的角色创建业务流程，专门设计为供其他业务流程引用的标准化组件。任何需要角色创建功能的业务流程（如项目创建、角色绑定等）都可以通过参数化调用这个统一的角色创建流程。所有UI交互统一由"Py视频创作工具前端"处理，确保用户体验的一致性。支持多种调用模式：全角色列表、条件筛选、快速创建等，通过不同参数实现不同的业务表现。
        </div>

        <div class="reference-box">
            <h4>📋 引用说明</h4>
            <p><strong>本流程可被以下业务流程引用：</strong></p>
            <ul>
                <li>• diagram-32-python-character-binding.html（绑定角色时需要新建角色）</li>
                <li>• 任何其他需要角色创建功能的业务流程</li>
            </ul>
            <p><strong>调用方式：</strong>通过标准化的参数接口调用，支持不同的显示模式和筛选条件。</p>
        </div>

        <div class="mermaid">
sequenceDiagram
    participant Caller as 调用方业务流程
    participant F as Py视频创作工具前端
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant RM as AI资源管理服务
    participant AI as AI平台
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线

    Note over Caller: 🔄 业务流程需要角色创建功能

    Caller->>F: 调用角色创建流程<br/>参数: {mode, filters, callback}

    Note over F: 📋 参数化配置处理
    alt mode=full（全角色列表）
        Note over F: 显示所有可用角色，无筛选条件
    else mode=filtered（条件筛选）
        Note over F: 根据filters参数筛选角色<br/>如：projectType, style, permissions等
    else mode=quick（快速创建）
        Note over F: 使用预设配置，简化创建流程
    else mode=advanced（高级创建）
        Note over F: 提供完整的角色创建功能
    end

    Note over F: 🎨 统一UI处理
    F->>F: 弹出角色创建罩层界面
    F->>A: 获取角色创建初始数据

    Note over A: 🔐 Token验证流程
    Note over A: 引用 diagram-22-python-token-validation.html
    A->>A: 解析Token验证用户权限

    alt Token验证失败
        A->>F: 返回认证失败<br/>{<br/>  code: 401,<br/>  message: "认证失败",<br/>  data: null,<br/>  timestamp, request_id<br/>}
        F->>F: 显示登录提示
        F->>Caller: 返回失败结果<br/>{<br/>  code: 401,<br/>  message: "认证失败",<br/>  data: null,<br/>  timestamp, request_id<br/>}
    else Token验证通过
        A->>DB: 查询用户状态和权限
        F->>A: GET /py-api/characters/styles
        A->>DB: 查询角色风格列表
        DB->>A: 返回风格数据
        A->>F: 返回角色创建配置数据
        F->>F: 渲染角色创建界面

        Note over F: 🎭 角色创建模式选择
        F->>F: 根据参数显示对应的创建选项

        alt 用户选择自有角色模式
            F->>F: 显示文件上传界面
            Note over F: 用户上传角色图片，验证格式和大小
        else 用户选择智能角色模式
            F->>F: 显示角色描述输入界面
            Note over F: 用户输入角色描述和提示词
        end

        F->>F: 用户选择角色风格(根据mode参数决定可选范围)

        Note over F: 🤖 智能平台选择流程
        F->>A: POST /py-api/ai-models/select-platform<br/>business_type=character_generation, auto_recommend=true
        A->>A: 调用智能平台选择服务
        A->>SC: 调用AiServiceClient
        SC->>DB: 查询用户历史偏好和使用记录
        SC->>SC: 分析用户偏好+平台状态+任务特性
        SC->>A: 返回最佳推荐+备选方案
        A->>F: 返回平台推荐结果
        F->>F: 显示平台选择界面

        F->>F: 用户确认创建参数，点击生成按钮

        Note over F: 🔗 建立WebSocket连接
        F->>W: POST /py-api/websocket/auth<br/>认证WebSocket连接<br/>{<br/>  "client_type": "python_tool",<br/>  "user_token": "Bearer xxx",<br/>  "business_type": "character_creation"<br/>}
        W->>W: 验证用户Token和客户端类型
        W->>DB: 创建WebSocket会话记录
        W->>F: 返回WebSocket连接信息<br/>{<br/>  code: 200,<br/>  message: "WebSocket认证成功",<br/>  data: {<br/>    session_id: "ws_session_xxx",<br/>    websocket_url: "wss://api.tiptop.cn:8080",<br/>    expires_at: "2024-01-01T12:00:00Z"<br/>  }<br/>}
        F->>W: 连接到WebSocket服务器<br/>wss://api.tiptop.cn:8080?session_id=ws_session_xxx
        W->>F: WebSocket连接确认，准备接收进度推送

        F->>W: 角色创建请求
        W->>A: 转发角色创建请求

        Note over A: 📊 标准化积分处理流程
        A->>DB: 检查用户积分(事务锁定)

        alt 积分不足
            Note over A: 积分 < 所需积分
            A->>W: 返回积分不足详细信息<br/>{<br/>  code: 1006,<br/>  message: "积分不足",<br/>  data: null,<br/>  timestamp, request_id<br/>}
            W->>F: 推送积分不足消息
            F->>F: 显示积分不足提示
            F->>Caller: 返回失败结果<br/>{<br/>  code: 1006,<br/>  message: "积分不足",<br/>  data: null,<br/>  timestamp, request_id<br/>}
            Note over A: 无扣费操作，保护用户资金
        else 积分充足
            A->>DB: 扣取积分(冻结状态)
            A->>R: 同步积分状态(缓存更新)
            A->>DB: 写入业务日志(状态:冻结)
            A->>R: 缓存业务日志
            A->>RM: 创建角色元数据记录（仅URL/状态/元信息）

            Note over A: 📈 实时进度推送
            A->>W: 推送进度更新(10%, "开始角色创建")
            W->>F: 实时推送进度到前端
            F->>F: 更新进度条显示

            Note over A: 🎯 执行角色创建处理
            alt 自有角色模式
                A->>W: 推送进度更新(30%, "处理上传的角色图片")
                W->>F: 实时推送进度
                A->>SC: 调用图片处理服务
                SC->>SC: 处理用户上传的角色图片
                A->>W: 推送进度更新(60%, "应用角色风格")
                W->>F: 实时推送进度
            else 智能角色模式
                A->>W: 推送进度更新(30%, "连接AI平台")
                W->>F: 实时推送进度
                A->>SC: 调用AI生成服务
                A->>W: 推送进度更新(60%, "AI处理中")
                W->>F: 实时推送进度
                SC->>AI: 生成角色图像
                AI->>SC: 返回生成结果
                SC->>A: 返回生成URL与元数据
            end

            A->>W: 推送进度更新(80%, "保存角色数据")
            W->>F: 实时推送进度

            alt 角色创建失败
                A->>W: 返回失败结果<br/>{<br/>  code: 5002,<br/>  message: "角色生成失败",<br/>  data: error_details,<br/>  timestamp, request_id<br/>}
                W->>F: 推送失败结果
                A->>DB: 更新业务日志(状态:失败)
                A->>DB: 退还积分(解冻→退还)
                A->>R: 同步退还状态
                A->>E: POST /py-api/events/publish<br/>发布失败事件(异步处理)<br/>{<br/>  "event_type": "character_creation_failed",<br/>  "business_id": "character_task_id",<br/>  "user_id": user_id,<br/>  "error_details": {<br/>    "creation_mode": "intelligent",<br/>    "error_code": "AI_SERVICE_ERROR",<br/>    "error_message": "角色创建失败"<br/>  },<br/>  "metadata": {<br/>    "character_prompt": "...",<br/>    "generation_params": {...},<br/>    "platform": "liblib"<br/>  }<br/>}
                F->>F: 显示创建失败提示
                F->>Caller: 返回失败结果<br/>{<br/>  code: 5002,<br/>  message: "角色生成失败",<br/>  data: error_details,<br/>  timestamp, request_id<br/>}
                Note over A: 仅更新元数据状态与资金记账，遵循资源下载架构边界
            else 角色创建成功
                A->>DB: 更新业务日志(状态:成功)
                A->>DB: 确认积分扣取(解冻→已扣)
                A->>R: 同步最终状态
                A->>RM: 更新元数据（status=ready, url，仅URL/状态/元信息）

                A->>W: 推送进度更新(100%, "角色创建完成")
                W->>F: 实时推送最终完成状态
                F->>F: 显示创建成功界面

                A->>W: 返回角色数据
                W->>F: 推送角色创建成功结果
                F->>F: 关闭角色创建罩层
                F->>Caller: 返回成功结果<br/>{<br/>  code: 200,<br/>  message: "角色生成任务创建成功",<br/>  data: {<br/>    task_id, status,<br/>    estimated_cost, platform<br/>  },<br/>  timestamp, request_id<br/>}

                Note over A: 📊 用户偏好学习与优化
                A->>DB: 记录用户平台选择行为和偏好权重
                A->>R: 更新用户常用平台缓存
                A->>R: 刷新推荐算法缓存
            end
        end

        Note over F: 🔚 清理资源
        F->>W: 关闭WebSocket连接
    end

    Note over Caller: 🎯 接收角色创建结果，继续后续业务流程
        </div>

        <div class="features">
            <h3>🎯 可复用流程特性</h3>
            <ul>
                <li><strong>🔄 标准化调用接口：</strong>统一的参数化调用方式，支持不同业务场景的需求</li>
                <li><strong>🔐 标准化Token验证：</strong>复用diagram-22-python-token-validation.html标准Token验证流程</li>
                <li><strong>🎨 UI统一处理：</strong>所有角色创建的UI交互都由"Py视频创作工具前端"统一处理，确保用户体验一致性</li>
                <li><strong>📋 多模式支持：</strong>支持全角色列表、条件筛选、快速创建、高级创建等多种调用模式</li>
                <li><strong>🎭 双创建模式：</strong>支持自有角色上传和智能角色生成两种创建方式</li>
                <li><strong>🤖 智能平台选择：</strong>集成标准化的AI平台选择机制，提供最佳推荐</li>
                <li><strong>🔒 完整积分处理：</strong>包含积分验证、冻结、扣除、返还的完整流程</li>
                <li><strong>📡 实时进度推送：</strong>通过WebSocket提供实时的创建进度反馈</li>
                <li><strong>⚠️ 错误处理机制：</strong>完善的错误处理和用户提示机制</li>
                <li><strong>🔄 状态管理：</strong>完整的业务状态跟踪和数据同步</li>
                <li><strong>📊 用户偏好学习：</strong>记录用户行为，优化后续推荐</li>
                <li><strong>🎯 结果回调：</strong>标准化的成功/失败结果返回机制</li>
                <li><strong>🗂️ 资源管理边界（RM）：</strong>仅管理元数据（URL/状态/元信息），不保存实际资源文件</li>
                <li><strong>🔧 可扩展配置：</strong>支持灵活的参数配置和功能扩展</li>
            </ul>

            <h3>📋 调用参数规范</h3>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                <h4>🔧 调用接口</h4>
                <pre style="background-color: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;">
// 调用角色创建流程（2025-08-07 扩展版）
CharacterCreation.start({
    mode: 'full|filtered|quick|advanced',
    filters: {
        projectType: 'video|animation|...',
        style: ['写实风3.0', '动漫可爱3.0', '武侠古风3.0', ...], // 24种风格
        permissions: ['create', 'publish', ...],
        maxCount: 10,
        tags: ['male', 'female', 'modern', 'ancient', ...] // 新增标签筛选
    },
    config: {
        showStyleSelection: true,
        showModeSelection: true,
        enableQuickCreate: false,
        showQualityLevel: true,    // 新增：显示质量选择
        showProjectBinding: true,  // 新增：显示项目绑定
        showAutoBindOption: true   // 新增：显示自动绑定选项
    },
    callbacks: {
        onSuccess: (character) => { /* 成功回调，包含绑定信息 */ },
        onCancel: () => { /* 取消回调 */ },
        onError: (error) => { /* 错误回调 */ },
        onProgress: (progress) => { /* 进度回调，包含绑定进度 */ }
    }
});

// API接口调用示例（扩展参数）
// 1. AI生成角色（扩展版）
POST /py-api/characters/generate
{
    "prompt": "一个勇敢的战士",
    "gender": "male",
    "age_range": "adult",
    "personality": "brave",
    "platform": "deepseek",
    "style": "写实风3.0",           // 新增：风格选择
    "project_id": 123,             // 恢复：项目关联
    "mode": "full",                // 新增：调用模式
    "quality_level": "high",       // 新增：质量偏好
    "publish_to_library": true,    // 新增：发布选项
    "auto_bind": false,            // 新增：自动绑定
    "storyboard_position_id": "pos_123" // 新增：分镜位置
}

// 2. 基于文件创建角色（全新接口）
POST /py-api/characters/create-from-file
{
    "file_id": "file_123",         // FileController上传的文件ID
    "style": "动漫可爱3.0",
    "project_id": 123,
    "publish_to_library": true,
    "auto_bind": true,
    "storyboard_position_id": "pos_456"
}

// 3. 角色绑定（扩展版）
POST /py-api/characters/bind
{
    "character_id": 123,
    "reason": "分镜绑定",
    "storyboard_position_id": "pos_789",    // 新增：分镜位置
    "binding_context": "storyboard",       // 新增：绑定上下文
    "auto_bind": true,                     // 新增：自动绑定标识
    "compatibility_check": true            // 新增：兼容性检查
}
                </pre>

                <h4>📤 返回结果格式</h4>
                <pre style="background-color: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;">
// 成功结果
{
    success: true,
    character: {
        id: 'char_123456',
        name: '角色名称',
        style: '写实风3.0',
        mode: 'ai_created|user_owned',
        imageUrl: 'https://...',
        metadata: { ... }
    }
}

// 失败结果（遵循API规范格式）
{
    code: 1006,                    // 业务错误码：1006=积分不足, 401=认证失败, 422=参数验证失败, 404=资源不存在, 5002=控制器异常, 5003=服务层异常
    message: "积分不足",           // 业务错误码描述
    data: {                       // 错误详细数据（可选）
        error_details: 'specific_error_info',
        user_cancelled: false,
        creation_step: 'generation|processing|saving'
    },
    timestamp: 1640995200,        // 时间戳
    request_id: "req_abc123_def456" // 请求ID
}
                </pre>
            </div>

            <h3>🔗 引用示例</h3>
            <div style="background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin-top: 10px;">

                <h4>🔗 在角色绑定流程中引用</h4>
                <p>当绑定角色需要新建角色时，调用此流程：</p>
                <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 3px;">
// 在 diagram-32-python-character-binding.html 中
Note over F: 用户点击"新增角色"
F->>CharacterCreation: 调用角色创建流程(filtered模式)
Note over CharacterCreation: 引用 diagram-character-creation-shared.html
CharacterCreation->>F: 返回创建的角色数据
F->>BindingFlow: 自动绑定新创建的角色
                </pre>
            </div>

            <h3>📚 技术规范说明</h3>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                <p><strong>本流程遵循以下技术规范：</strong></p>
                <ul>
                    <li><strong>C-1：</strong>AI任务调度 - 智能平台选择机制</li>
                    <li><strong>C-2：</strong>AI生成成功 - 积分确认扣取流程</li>
                    <li><strong>C-3：</strong>积分不足 - 快速验证机制</li>
                    <li><strong>C-4：</strong>AI生成失败 - 事件总线异步处理</li>
                    <li><strong>C-6：</strong>资源管理 - AI资源管理服务</li>
                    <li><strong>C-7：</strong>资源下载 - 直接下载机制</li>
                    <li><strong>C-8：</strong>作品发布 - 可选发布流程</li>
                    <li><strong>C-9：</strong>环境切换 - AiServiceClient统一调用</li>
                </ul>
                <p><em>这些规范确保了与系统其他流程的一致性和兼容性。</em></p>
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35
            }
        });
    </script>
</body>
</html>
