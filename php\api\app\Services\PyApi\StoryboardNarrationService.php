<?php

namespace App\Services\PyApi;

use App\Enums\ApiCodeEnum;
use App\Helpers\LogCheckHelper;
use App\Models\StoryboardNarration;
use App\Models\ProjectStoryboard;
use App\Models\ProjectCharacter;
use App\Services\PyApi\AiServiceClient;
use App\Services\PyApi\VoiceService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * 分镜旁白/解说服务
 * 
 * 🚨 架构边界规范：
 * ✅ 仅存储元数据与URL，禁止中转音频资源
 * ✅ 开发阶段调用 aiapi，上线后通过 ai.php 切换真实平台
 * ✅ 统一使用 PyApi\AuthService 认证
 * ✅ 遵循资源下载架构边界
 */
class StoryboardNarrationService
{
    protected AiServiceClient $aiServiceClient;
    protected VoiceService $voiceService;

    public function __construct(AiServiceClient $aiServiceClient, VoiceService $voiceService)
    {
        $this->aiServiceClient = $aiServiceClient;
        $this->voiceService = $voiceService;
    }

    /**
     * 设置分镜的解说角色
     * 
     * @param int $storyboardId 分镜ID
     * @param int $userId 用户ID（用于权限校验）
     * @param int $narratorCharacterId 解说角色ID
     * @param string|null $subtitleText 可选旁白文本（不传则沿用分镜默认）
     * @return array
     * @throws Exception
     */
    public function setNarrationCharacter(int $storyboardId, int $userId, int $narratorCharacterId, ?string $subtitleText = null): array
    {
        try {
            // 权限校验：检查用户是否有权访问该分镜
            $storyboard = ProjectStoryboard::with('project')
                ->where('id', $storyboardId)
                ->first();

            if (!$storyboard) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '分镜不存在',
                    'data' => null
                ];
            }

            if ($storyboard->project->user_id !== $userId) {
                return [
                    'code' => ApiCodeEnum::FORBIDDEN,
                    'message' => '无权访问该分镜',
                    'data' => null
                ];
            }
            
            // 验证解说角色是否属于该项目
            $character = ProjectCharacter::where('id', $narratorCharacterId)
                ->where('project_id', $storyboard->project_id)
                ->first();
                
            if (!$character) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '指定的解说角色不存在或不属于该项目',
                    'data' => null
                ];
            }

            DB::beginTransaction();

            // 查找或创建旁白记录
            $narration = StoryboardNarration::firstOrNew(['storyboard_id' => $storyboardId]);

            // 更新字段
            $narration->narrator_character_id = $narratorCharacterId;
            if ($subtitleText !== null) {
                $narration->subtitle_text = $subtitleText;
            }
            $narration->status = StoryboardNarration::STATUS_ACTIVE;

            $narration->save();

            DB::commit();

            // 返回给控制器数据规划
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '解说角色设置成功',
                'data' => $narration->load('narratorCharacter')->toApiArray()
            ];

        } catch (Exception $e) {
            DB::rollBack();

            $error_context = [
                'storyboard_id' => $storyboardId,
                'narrator_character_id' => $narratorCharacterId,
                'user_id' => $userId,
                'subtitle_text' => $subtitleText,
            ];

            Log::error('设置分镜解说角色失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '设置分镜解说角色失败',
                'data' => null
            ];
        }
    }

    /**
     * 设置分镜旁白的音色
     * 
     * @param int $storyboardId 分镜ID
     * @param int $userId 用户ID
     * @param string $voiceId 音色ID
     * @param array $options 可选参数 [platform, language, emotion, speed, pitch]
     * @return array
     * @throws Exception
     */
    public function setNarrationVoice(int $storyboardId, int $userId, string $voiceId, array $options = []): array
    {
        try {
            // 权限校验
            $storyboard = ProjectStoryboard::with('project')
                ->where('id', $storyboardId)
                ->first();

            if (!$storyboard) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '分镜不存在',
                    'data' => null
                ];
            }

            if ($storyboard->project->user_id !== $userId) {
                return [
                    'code' => ApiCodeEnum::FORBIDDEN,
                    'message' => '无权访问该分镜',
                    'data' => null
                ];
            }

            // 参数校验与默认值
            $platform = $options['platform'] ?? 'volcengine';
            $language = $options['language'] ?? 'zh-CN';
            $emotion = $options['emotion'] ?? 'neutral';
            $speed = $options['speed'] ?? 1.0;
            $pitch = $options['pitch'] ?? 1.0;

            // 校验参数范围
            $validationResult = $this->validateVoiceParameters($language, $emotion, $speed, $pitch);
            if ($validationResult !== null) {
                return $validationResult;
            }

            DB::beginTransaction();

            // 查找或创建旁白记录
            $narration = StoryboardNarration::firstOrNew(['storyboard_id' => $storyboardId]);
            
            // 更新音色配置
            $narration->voice_id = $voiceId;
            $narration->platform = $platform;
            $narration->language = $language;
            $narration->emotion = $emotion;
            $narration->speed = $speed;
            $narration->pitch = $pitch;
            $narration->status = StoryboardNarration::STATUS_ACTIVE;
            
            $narration->save();

            DB::commit();

            // 返回给控制器数据规划
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '音色设置成功',
                'data' => $narration->load('narratorCharacter')->toApiArray()
            ];

        } catch (Exception $e) {
            DB::rollBack();

            $error_context = [
                'storyboard_id' => $storyboardId,
                'voice_id' => $voiceId,
                'user_id' => $userId,
                'options' => $options,
            ];

            Log::error('设置分镜旁白音色失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '设置分镜旁白音色失败',
                'data' => null
            ];
        }
    }

    /**
     * 生成试听音频
     * 
     * @param int $storyboardId 分镜ID
     * @param int $userId 用户ID
     * @param string|null $text 可选文本（不传则使用分镜默认旁白文本）
     * @return array
     * @throws Exception
     */
    public function generatePreview(int $storyboardId, int $userId, ?string $text = null): array
    {
        try {
            // 权限校验
            $storyboard = ProjectStoryboard::with('project')
                ->where('id', $storyboardId)
                ->first();

            if (!$storyboard) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '分镜不存在',
                    'data' => null
                ];
            }

            if ($storyboard->project->user_id !== $userId) {
                return [
                    'code' => ApiCodeEnum::FORBIDDEN,
                    'message' => '无权访问该分镜',
                    'data' => null
                ];
            }

            // 获取旁白配置
            $narration = StoryboardNarration::byStoryboard($storyboardId)->active()->first();

            if (!$narration || !$narration->hasVoiceConfigured()) {
                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => '请先配置音色后再试听',
                    'data' => null
                ];
            }

            // 确定试听文本（优先使用传入文本，否则使用配置的旁白文本）
            $previewText = $text ?? $narration->getEffectiveSubtitleText();

            if (empty($previewText)) {
                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => '无可用的旁白文本进行试听',
                    'data' => null
                ];
            }

            // 🚨 架构修复：调用VoiceService而不是直接调用AiServiceClient
            $voiceConfig = [
                'voice_id' => $narration->voice_id,
                'platform' => $narration->platform,
                'language' => $narration->language,
                'emotion' => $narration->emotion,
                'speed' => $narration->speed,
                'pitch' => $narration->pitch
            ];

            $voiceResult = $this->voiceService->generateVoicePreview(
                $userId,
                $voiceConfig,
                $previewText,
                $storyboard->project_id
            );

            if ($voiceResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $voiceResult; // 直接返回VoiceService的错误结果
            }

            // 更新最后试听URL（便于前端复用）
            $narration->last_preview_url = $voiceResult['data']['audio_url'];
            $narration->save();

            // 返回给控制器数据规划
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '试听音频生成成功',
                'data' => [
                    'audio_url' => $voiceResult['data']['audio_url'],
                    'preview_text' => $previewText,
                    'voice_config' => [
                        'voice_id' => $narration->voice_id,
                        'platform' => $narration->platform,
                        'language' => $narration->language,
                        'emotion' => $narration->emotion,
                        'speed' => $narration->speed,
                        'pitch' => $narration->pitch
                    ],
                    'narration' => $narration->toApiArray(),
                    'cost' => $voiceResult['data']['cost'] ?? 0 // 添加成本信息
                ]
            ];

        } catch (Exception $e) {
            $error_context = [
                'storyboard_id' => $storyboardId,
                'user_id' => $userId,
                'text' => $text,
            ];

            Log::error('生成分镜旁白试听失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '生成分镜旁白试听失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取分镜旁白配置
     *
     * @param int $storyboardId 分镜ID
     * @param int $userId 用户ID
     * @return array
     */
    public function getNarration(int $storyboardId, int $userId): array
    {
        try {
            // 权限校验
            $storyboard = ProjectStoryboard::with('project')
                ->where('id', $storyboardId)
                ->first();

            if (!$storyboard) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '分镜不存在',
                    'data' => null
                ];
            }

            if ($storyboard->project->user_id !== $userId) {
                return [
                    'code' => ApiCodeEnum::FORBIDDEN,
                    'message' => '无权访问该分镜',
                    'data' => null
                ];
            }

            // 获取旁白配置
            $narration = StoryboardNarration::byStoryboard($storyboardId)
                ->with('narratorCharacter')
                ->first();

            // 返回给控制器数据规划
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '获取成功',
                'data' => $narration ? $narration->toApiArray() : null
            ];

        } catch (Exception $e) {
            $error_context = [
                'storyboard_id' => $storyboardId,
                'user_id' => $userId,
            ];

            Log::error('获取分镜旁白配置失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取分镜旁白配置失败',
                'data' => null
            ];
        }
    }

    /**
     * 校验音色参数
     */
    private function validateVoiceParameters(string $language, string $emotion, float $speed, float $pitch): ?array
    {
        if (!in_array($language, StoryboardNarration::SUPPORTED_LANGUAGES)) {
            return [
                'code' => ApiCodeEnum::VALIDATION_ERROR,
                'message' => '不支持的语言：' . $language,
                'data' => null
            ];
        }

        if (!in_array($emotion, StoryboardNarration::SUPPORTED_EMOTIONS)) {
            return [
                'code' => ApiCodeEnum::VALIDATION_ERROR,
                'message' => '不支持的情绪：' . $emotion,
                'data' => null
            ];
        }

        if ($speed < 0.5 || $speed > 2.0) {
            return [
                'code' => ApiCodeEnum::VALIDATION_ERROR,
                'message' => '语速范围应在 0.5-2.0 之间',
                'data' => null
            ];
        }

        if ($pitch < 0.5 || $pitch > 2.0) {
            return [
                'code' => ApiCodeEnum::VALIDATION_ERROR,
                'message' => '音调范围应在 0.5-2.0 之间',
                'data' => null
            ];
        }

        return null; // 验证通过
    }
}
