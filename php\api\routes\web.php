<?php

/** @var \Laravel\Lumen\Routing\Router $router */

/*
|--------------------------------------------------------------------------
| Application Routes
|--------------------------------------------------------------------------
|
| Here is where you can register all of the routes for an application.
| It is a breeze. Simply tell <PERSON><PERSON> the URIs it should respond to
| and give it the Closure to call when that URI is requested.
|
*/

$router->get('/', 'IndexController@index');

// API文档相关路由 - 支持动态加载架构
$router->get('/py-api-data', 'PyApiDocumentController@getApiData');                    // 获取所有API数据
$router->get('/py-api-list', 'PyApiDocumentController@getControllerList');             // 获取控制器列表
$router->get('/py-api-controller/{name}', 'PyApiDocumentController@getControllerData'); // 获取单个控制器数据

$router->get('/web-api-data', 'WebApiDocumentController@getApiData');                    // 获取所有API数据
$router->get('/web-api-list', 'WebApiDocumentController@getControllerList');             // 获取控制器列表
$router->get('/web-api-controller/{name}', 'WebApiDocumentController@getControllerData'); // 获取单个控制器数据

$router->get('/admin-api-data', 'AdminApiDocumentController@getApiData');                    // 获取所有API数据
$router->get('/admin-api-list', 'AdminApiDocumentController@getControllerList');             // 获取控制器列表
$router->get('/admin-api-controller/{name}', 'AdminApiDocumentController@getControllerData'); // 获取单个控制器数据



// ========================================================================
// ========================================================================
// ========================================================================


// ==================== PY_API 阶段1：公开路由（无需认证）====================
$router->group(['prefix' => 'py-api'], function () use ($router) {

    // 用户认证相关路由（已存在）
    $router->post('/auth/login', 'PyApi\AuthController@login');
    $router->post('/auth/register', 'PyApi\AuthController@register');
    $router->get('/auth/verify', 'PyApi\AuthController@verify'); // 更新：check -> verify

    // 🚨 CogniDev新增：基于七重同步铁律实施
    $router->post('/auth/logout', 'PyApi\AuthController@logout');
    $router->post('/auth/forgot-password', 'PyApi\AuthController@forgotPassword');
    $router->post('/auth/reset-password', 'PyApi\AuthController@resetPassword');

    // 设备管理路由（需要认证）
    $router->get('/devices/list', 'PyApi\DeviceController@getDevices');
    $router->post('/devices/revoke', 'PyApi\DeviceController@revokeDevice');
    $router->post('/devices/revoke-all', 'PyApi\DeviceController@revokeAllDevices');
    $router->get('/devices/token-status', 'PyApi\DeviceController@getTokenStatus');
    $router->post('/devices/login', 'PyApi\DeviceController@deviceLogin');

    // 缓存信息路由（公开访问）
    $router->get('/cache/stats', 'PyApi\CacheController@getStats');
    $router->get('/cache/keys', 'PyApi\CacheController@getKeys');
    $router->get('/cache/get', 'PyApi\CacheController@getValue');
    $router->get('/cache/config', 'PyApi\CacheController@getConfig');

    // 风格信息路由（公开访问）
    $router->get('/styles/list', 'PyApi\StyleController@list');
    $router->get('/styles/popular', 'PyApi\StyleController@popular');
    $router->get('/styles/{id}', 'PyApi\StyleController@detail');
});

// ==================== PY_API 阶段2：认证路由（需要登录）====================
// 注意：认证通过控制器内的AuthService::authenticate()方法处理，不使用中间件
$router->group(['prefix' => 'py-api'], function () use ($router) {

    // ========== 用户管理相关路由 ==========
    $router->get('/user/profile', 'PyApi\UserController@profile');
    $router->put('/user/profile', 'PyApi\UserController@updateProfile');
    $router->get('/user/preferences', 'PyApi\UserController@getPreferences');
    $router->put('/user/preferences', 'PyApi\UserController@updatePreferences');

    // ========== 积分管理相关路由 ==========
    $router->get('/points/balance', 'PyApi\PointsController@balance');
    $router->post('/points/recharge', 'PyApi\PointsController@recharge');
    $router->get('/points/transactions', 'PyApi\PointsController@transactions');

    // 积分高级管理
    $router->post('/credits/check', 'PyApi\CreditsController@checkCredits');
    $router->post('/credits/freeze', 'PyApi\CreditsController@freezeCredits');
    $router->post('/credits/refund', 'PyApi\CreditsController@refundCredits');

    // ========== AI模型管理相关路由 ==========
    $router->get('/ai-models/available', 'PyApi\AiModelController@available');
    $router->get('/ai-models/favorites', 'PyApi\AiModelController@favorites');
    $router->get('/ai-models/list', 'PyApi\AiModelController@list');
    $router->post('/ai-models/switch', 'PyApi\AiModelController@switch');
    $router->get('/ai-models/usage-stats', 'PyApi\AiModelController@usageStats');
    $router->get('/ai-models/{model_id}/detail', 'PyApi\AiModelController@detail');
    $router->post('/ai-models/{model_id}/test', 'PyApi\AiModelController@test');
    $router->post('/ai-models/{model_id}/favorite', 'PyApi\AiModelController@favorite');

    // 🔧 新增：智能平台选择和健康监控路由
    $router->post('/ai-models/select-platform', 'PyApi\AiModelController@selectOptimalPlatform');
    $router->get('/ai-models/platforms-health', 'PyApi\AiModelController@getAllPlatformsHealth');
    $router->get('/ai-models/platform-health/{platform}', 'PyApi\AiModelController@checkPlatformHealth');
    $router->get('/ai-models/platform-stats/{platform}', 'PyApi\AiModelController@getPlatformStats');

    // 🚨 CogniDev新增：里程碑2接口1 - 平台性能对比
    $router->get('/ai-models/platform-comparison', 'PyApi\AiModelController@platformComparison');

    // 🚨 CogniDev新增：里程碑2接口2 - 按业务类型获取平台
    $router->get('/ai-models/business-platforms', 'PyApi\AiModelController@businessPlatforms');

    // 🚨 升级：通用平台选择和推荐接口
    $router->get('/ai-models/platform-options', 'PyApi\AiModelController@getPlatformOptions');
    $router->get('/ai-models/user-recommendations', 'PyApi\AiModelController@getUserRecommendations');

    // ========== 素材管理相关路由 ==========
    $router->get('/assets/list', 'PyApi\AssetController@list');
    $router->post('/assets/upload', 'PyApi\AssetController@upload');
    $router->get('/assets/{id}', 'PyApi\AssetController@show');
    $router->delete('/assets/{id}', 'PyApi\AssetController@delete');

    // ========== 缓存管理相关路由 ==========
    $router->delete('/cache/clear', 'PyApi\CacheController@clearCache');
    $router->post('/cache/warmup', 'PyApi\CacheController@warmupCache');
    $router->put('/cache/set', 'PyApi\CacheController@setValue');
    $router->delete('/cache/delete', 'PyApi\CacheController@deleteKeys');

    // 角色库管理
    $router->get('/characters/categories', 'PyApi\CharacterController@getCategories');
    $router->get('/characters/list', 'PyApi\CharacterController@getLibrary');
    $router->get('/characters/recommendations', 'PyApi\CharacterController@getRecommendations');
    $router->get('/characters/bindings', 'PyApi\CharacterController@getMyBindings');
    $router->post('/characters/bind', 'PyApi\CharacterController@bindCharacter');
    $router->delete('/characters/unbind', 'PyApi\CharacterController@unbindCharacter');
    $router->post('/characters/generate', 'PyApi\CharacterController@generate');
    $router->post('/characters/generate-image', 'PyApi\CharacterController@generateImage');
    $router->post('/characters/create-from-file', 'PyApi\CharacterController@createFromFile');
    $router->post('/characters/create-with-websocket', 'PyApi\CharacterController@createWithWebSocket');
    $router->put('/characters/bindings/{id}', 'PyApi\CharacterController@updateBinding');
    $router->get('/characters/{id}', 'PyApi\CharacterController@getCharacterDetail');

    // ========== 配置管理相关路由 ==========
    $router->get('/config', 'PyApi\ConfigController@index');
    $router->get('/config/public', 'PyApi\ConfigController@getPublicConfig');
    $router->put('/config/batch', 'PyApi\ConfigController@batchUpdate');
    $router->post('/config/validate', 'PyApi\ConfigController@validateConfig');
    $router->post('/config/{id}/reset', 'PyApi\ConfigController@reset');
    $router->get('/config/{id}/history', 'PyApi\ConfigController@history');
    $router->put('/config/{id}', 'PyApi\ConfigController@update');

    // ========== 通知管理相关路由 ==========
    $router->get('/notifications', 'PyApi\NotificationController@index');
    $router->put('/notifications/mark-read', 'PyApi\NotificationController@markAsRead');
    $router->put('/notifications/mark-all-read', 'PyApi\NotificationController@markAllAsRead');
    $router->get('/notifications/stats', 'PyApi\NotificationController@stats');
    $router->post('/notifications/send', 'PyApi\NotificationController@send');
    $router->delete('/notifications/{id}', 'PyApi\NotificationController@destroy');

    // ========== 模板管理相关路由 ==========
    $router->post('/templates/create', 'PyApi\TemplateController@create');
    $router->get('/templates/marketplace', 'PyApi\TemplateController@marketplace');
    $router->get('/templates/my-templates', 'PyApi\TemplateController@myTemplates');
    $router->post('/templates/{id}/use', 'PyApi\TemplateController@use');
    $router->get('/templates/{id}/detail', 'PyApi\TemplateController@detail');
    $router->put('/templates/{id}', 'PyApi\TemplateController@update');
    $router->delete('/templates/{id}', 'PyApi\TemplateController@delete');

    // ========== 统一任务管理相关路由(整合了TaskManagementController和AiTaskController功能) ==========
    $router->get('/tasks', 'PyApi\AiTaskController@index');                       // 获取任务列表
    $router->get('/tasks/stats', 'PyApi\AiTaskController@stats');                 // 获取任务统计
    $router->get('/tasks/timeout-config', 'PyApi\AiTaskController@timeoutConfig'); // 获取超时配置
    $router->get('/tasks/batch/status', 'PyApi\AiTaskController@batchStatus');    // 批量查询任务状态
    $router->post('/tasks/{id}/cancel', 'PyApi\AiTaskController@cancel');         // 取消任务
    $router->post('/tasks/{id}/retry', 'PyApi\AiTaskController@retry');           // 重试任务
    $router->get('/tasks/{id}/recovery', 'PyApi\AiTaskController@recovery');      // 查询任务恢复状态
    $router->get('/tasks/{id}', 'PyApi\AiTaskController@show');                   // 获取任务详情

    // ========== 版本管理相关路由 ==========
    $router->get('/versions/compare', 'PyApi\VersionController@compare');
    $router->post('/resources/{id}/versions', 'PyApi\VersionController@create');
    $router->get('/resources/{id}/versions', 'PyApi\VersionController@list');
    $router->get('/versions/{id}', 'PyApi\VersionController@show');
    $router->put('/versions/{id}/set-current', 'PyApi\VersionController@setCurrent');
    $router->delete('/versions/{id}', 'PyApi\VersionController@delete');

    // ========== 下载管理相关路由 ==========
    $router->get('/downloads/list', 'PyApi\DownloadController@list');
    $router->get('/downloads/statistics', 'PyApi\DownloadController@statistics');
    $router->post('/downloads/create-link', 'PyApi\DownloadController@createLink');
    $router->post('/downloads/batch', 'PyApi\DownloadController@batchDownload');
    $router->post('/downloads/cleanup', 'PyApi\DownloadController@cleanup');
    $router->get('/downloads/secure/{token}', 'PyApi\DownloadController@secureDownload');
    $router->post('/downloads/{id}/retry', 'PyApi\DownloadController@retry');

    // ========== 文件管理相关路由 ==========
    $router->post('/files/upload', 'PyApi\FileController@upload');
    $router->get('/files/list', 'PyApi\FileController@getFiles');
    $router->get('/files/{id}', 'PyApi\FileController@getFileDetail');
    $router->delete('/files/{id}', 'PyApi\FileController@deleteFile');
    $router->get('/files/{id}/download', 'PyApi\FileController@downloadFile');

    // ========== 日志管理相关路由 ==========
    $router->get('/logs/system', 'PyApi\LogController@systemLogs');
    $router->get('/logs/user-actions', 'PyApi\LogController@userActionLogs');
    $router->get('/logs/ai-calls', 'PyApi\LogController@aiCallLogs');
    $router->get('/logs/errors', 'PyApi\LogController@errorLogs');
    $router->post('/logs/export', 'PyApi\LogController@exportLogs');
    $router->put('/logs/errors/{id}/resolve', 'PyApi\LogController@resolveError');

    // ========== 项目管理相关路由 ==========
    // 基础项目管理（ProjectController - 专注内容创作项目）
    $router->post('/projects/create-with-story', 'PyApi\ProjectController@createWithStory');
    $router->post('/projects/create-with-websocket', 'PyApi\ProjectController@createWithWebSocket');
    $router->get('/projects/my-projects', 'PyApi\ProjectController@myProjects');
    $router->get('/projects/list', 'PyApi\ProjectController@list');
    $router->post('/projects/create', 'PyApi\ProjectController@create');
    $router->put('/projects/{id}/confirm-title', 'PyApi\ProjectController@confirmTitle');
    $router->get('/projects/{id}', 'PyApi\ProjectController@detail');
    $router->put('/projects/{id}', 'PyApi\ProjectController@update');
    $router->delete('/projects/{id}', 'PyApi\ProjectController@delete');

    // 项目分镜管理（ProjectStoryboardController - 分镜CRUD操作）
    $router->get('/projects/{project_id}/storyboards', 'PyApi\ProjectStoryboardController@index');
    $router->post('/projects/{project_id}/storyboards', 'PyApi\ProjectStoryboardController@store');
    $router->get('/storyboards/{id}', 'PyApi\ProjectStoryboardController@show');
    $router->put('/storyboards/{id}', 'PyApi\ProjectStoryboardController@update');
    $router->delete('/storyboards/{id}', 'PyApi\ProjectStoryboardController@destroy');

    $router->get('/storyboards/{id}/narration', 'PyApi\StoryboardNarrationController@getNarration');
    $router->put('/storyboards/{id}/narration', 'PyApi\StoryboardNarrationController@setNarrationCharacter');
    $router->put('/storyboards/{id}/narration/voice', 'PyApi\StoryboardNarrationController@setNarrationVoice');
    $router->put('/projects/{project_id}/storyboards/reorder', 'PyApi\ProjectStoryboardController@reorder');
    $router->post('/projects/{project_id}/storyboards/batch-generate', 'PyApi\ProjectStoryboardController@batchGenerate');
    $router->get('/storyboards/tasks/{task_id}/status', 'PyApi\ProjectStoryboardController@getTaskStatus');
    $router->post('/storyboards/ai-platforms/recommend', 'PyApi\ProjectStoryboardController@recommendAiPlatforms');

    // ========== 事件管理相关路由 ==========
    $router->post('/events/publish', 'PyApi\EventController@publish');
    $router->get('/events', 'PyApi\EventController@list');


    // 项目角色管理（ProjectCharacterController - 角色绑定功能）
    $router->get('/projects/{project_id}/characters', 'PyApi\ProjectCharacterController@index');
    $router->put('/characters/{id}/bind', 'PyApi\ProjectCharacterController@bind');
    $router->delete('/characters/{id}/unbind', 'PyApi\ProjectCharacterController@unbind');
    $router->post('/projects/{project_id}/characters/auto-match', 'PyApi\ProjectCharacterController@autoMatch');
    $router->post('/projects/{project_id}/characters/batch-bind', 'PyApi\ProjectCharacterController@batchBind');

    // 高级项目管理（ProjectManagementController - 专注项目管理功能）
    $router->post('/project-management/tasks', 'PyApi\ProjectManagementController@createTask');
    $router->get('/project-management/progress', 'PyApi\ProjectManagementController@getProgress');
    $router->post('/project-management/assign-resources', 'PyApi\ProjectManagementController@assignResources');
    $router->get('/project-management/statistics', 'PyApi\ProjectManagementController@getStatistics');
    $router->post('/project-management/collaborate', 'PyApi\ProjectManagementController@collaborate');
    $router->get('/project-management/milestones', 'PyApi\ProjectManagementController@getMilestones');

    // ========== 分镜动作库相关路由 ==========
    // 动作库列表和详情
    $router->get('/storyboard-actions', 'PyApi\StoryboardActionLibraryController@index');
    $router->get('/storyboard-actions/{id}', 'PyApi\StoryboardActionLibraryController@show');

    // 动作创建、更新、删除
    $router->post('/storyboard-actions', 'PyApi\StoryboardActionLibraryController@store');
    $router->put('/storyboard-actions/{id}', 'PyApi\StoryboardActionLibraryController@update');
    $router->delete('/storyboard-actions/{id}', 'PyApi\StoryboardActionLibraryController@destroy');

    // 动作搜索和筛选
    $router->get('/storyboard-actions/search', 'PyApi\StoryboardActionLibraryController@search');
    $router->get('/storyboard-actions/by-camera-shot/{camera_shot}', 'PyApi\StoryboardActionLibraryController@byCameraShot');

    // 动作使用和统计
    $router->post('/storyboard-actions/{id}/increment-usage', 'PyApi\StoryboardActionLibraryController@incrementUsage');
    $router->get('/storyboard-actions/statistics', 'PyApi\StoryboardActionLibraryController@statistics');
    $router->get('/storyboard-actions/options', 'PyApi\StoryboardActionLibraryController@options');

    // ========== AI生成相关路由(专注AI任务的创建和执行，向第三方AI平台（虚拟AI API服务）发起请求) ==========
    // 文本生成
    $router->post('/ai/text/generate', 'PyApi\AiGenerationController@generateText');
    $router->post('/ai/text/generate-with-websocket', 'PyApi\AiGenerationController@generateTextWithWebSocket');
    $router->get('/ai/tasks', 'PyApi\AiGenerationController@getUserTasks');
    $router->get('/ai/tasks/{id}', 'PyApi\AiGenerationController@getTaskStatus');
    $router->post('/ai/tasks/{id}/retry', 'PyApi\AiGenerationController@retryTask');

    // 图像生成
    $router->post('/images/generate', 'PyApi\ImageController@generate');
    $router->post('/images/batch-generate', 'PyApi\ImageController@batchGenerate');
    $router->get('/images/{id}/status', 'PyApi\ImageController@getStatus');
    $router->get('/images/{id}/result', 'PyApi\ImageController@getResult');

    // 音频处理
    $router->post('/audio/mix', 'PyApi\AudioController@mix');
    $router->post('/audio/enhance', 'PyApi\AudioController@enhance');
    $router->get('/audio/mix/{id}/status', 'PyApi\AudioController@getMixStatus');
    $router->get('/audio/enhance/{id}/status', 'PyApi\AudioController@getEnhanceStatus');

    // 音乐生成
    $router->post('/music/generate', 'PyApi\MusicController@generate');
    $router->post('/music/batch-generate', 'PyApi\MusicController@batchGenerate');
    $router->get('/music/{id}/status', 'PyApi\MusicController@getStatus');
    $router->get('/music/{id}/result', 'PyApi\MusicController@getResult');

    // 声音生成
    $router->post('/sounds/generate', 'PyApi\SoundController@generate');
    $router->post('/sounds/batch-generate', 'PyApi\SoundController@batchGenerate');
    $router->get('/sounds/{id}/status', 'PyApi\SoundController@getStatus');
    $router->get('/sounds/{id}/result', 'PyApi\SoundController@getResult');

    // 视频生成
    $router->post('/videos/generate', 'PyApi\VideoController@generate');
    $router->get('/videos/{id}/status', 'PyApi\VideoController@getStatus');
    $router->get('/videos/{id}/result', 'PyApi\VideoController@getResult');

    // 语音合成
    $router->post('/voice/synthesize', 'PyApi\VoiceController@synthesize');
    $router->post('/voice/batch-synthesize', 'PyApi\VoiceController@batchSynthesize');
    $router->post('/voice/clone', 'PyApi\VoiceController@clone');
    $router->post('/voice/custom', 'PyApi\VoiceController@custom');
    $router->get('/voice/custom/{id}/status', 'PyApi\VoiceController@getCustomStatus');
    $router->get('/voice/clone/{id}/status', 'PyApi\VoiceController@getCloneStatus');
    $router->get('/voice/{id}/status', 'PyApi\VoiceController@getStatus');
    $router->get('/voices/providers/volcengine/list', 'PyApi\VoiceController@getVolcengineVoiceList');
    $router->post('/voice/preview', 'PyApi\VoiceController@preview');
    $router->post('/voice/preview-with-websocket', 'PyApi\VoiceController@previewWithWebSocket');

    // ========== 故事生成相关路由 ==========
    $router->post('/stories/generate', 'PyApi\StoryController@generate');
    $router->get('/stories/{id}/status', 'PyApi\StoryController@getStatus');

    // ========== 资源管理相关路由 ==========
    $router->post('/resources/generate', 'PyApi\ResourceController@generate');
    $router->get('/resources/list', 'PyApi\ResourceController@list');
    $router->get('/resources/my-resources', 'PyApi\ResourceController@myResources');
    $router->post('/resources/batch-generate', 'PyApi\ResourceController@batchGenerate');
    $router->put('/resources/{id}/status', 'PyApi\ResourceController@updateStatus');
    $router->get('/resources/{id}/status', 'PyApi\ResourceController@getStatus');
    $router->get('/resources/{id}/download-info', 'PyApi\ResourceController@getDownloadInfo');
    $router->post('/resources/{id}/confirm-download', 'PyApi\ResourceController@confirmDownload');
    $router->delete('/resources/{id}', 'PyApi\ResourceController@delete');

    // ========== 发布管理相关路由 ==========
    $router->post('/publications/publish', 'PyApi\PublicationController@publish');
    $router->get('/publications/my-publications', 'PyApi\PublicationController@myPublications');
    $router->get('/publications/plaza', 'PyApi\PublicationController@plaza');
    $router->get('/publications/trending', 'PyApi\PublicationController@trending');
    $router->get('/publications/{id}/status', 'PyApi\PublicationController@getStatus');
    $router->post('/publications/{id}/unpublish', 'PyApi\PublicationController@unpublish');
    $router->get('/publications/{id}/detail', 'PyApi\PublicationController@detail');
    $router->put('/publications/{id}', 'PyApi\PublicationController@update');
    $router->delete('/publications/{id}', 'PyApi\PublicationController@delete');

    // ========== 推荐系统相关路由 ==========
    $router->get('/recommendations/content', 'PyApi\RecommendationController@content');
    $router->get('/recommendations/users', 'PyApi\RecommendationController@users');
    $router->get('/recommendations/topics', 'PyApi\RecommendationController@topics');
    $router->post('/recommendations/feedback', 'PyApi\RecommendationController@feedback');
    $router->get('/recommendations/preferences', 'PyApi\RecommendationController@preferences');
    $router->put('/recommendations/preferences', 'PyApi\RecommendationController@updatePreferences');
    $router->get('/recommendations/analytics', 'PyApi\RecommendationController@analytics');
    $router->get('/recommendations/personalized', 'PyApi\RecommendationController@personalized');

    // ========== 审核管理相关路由 ==========
    $router->post('/reviews/submit', 'PyApi\ReviewController@submit');
    $router->get('/reviews/my-reviews', 'PyApi\ReviewController@myReviews');
    $router->get('/reviews/queue-status', 'PyApi\ReviewController@queueStatus');
    $router->get('/reviews/guidelines', 'PyApi\ReviewController@guidelines');
    $router->post('/reviews/pre-check', 'PyApi\ReviewController@preCheck');
    $router->get('/reviews/{id}/status', 'PyApi\ReviewController@getStatus');
    $router->post('/reviews/{id}/appeal', 'PyApi\ReviewController@appeal');

    // ========== 社交功能相关路由 ==========
    $router->post('/social/follow', 'PyApi\SocialController@follow');
    $router->get('/social/follows', 'PyApi\SocialController@getFollows');
    $router->post('/social/like', 'PyApi\SocialController@like');
    $router->post('/social/comment', 'PyApi\SocialController@comment');
    $router->post('/social/share', 'PyApi\SocialController@share');
    $router->get('/social/feed', 'PyApi\SocialController@getFeed');
    $router->get('/social/notifications', 'PyApi\SocialController@getNotifications');
    $router->put('/social/notifications/read', 'PyApi\SocialController@markNotificationsRead');
    $router->get('/social/stats', 'PyApi\SocialController@getStats');
    $router->get('/social/{id}/comments', 'PyApi\SocialController@getComments');

    // ========== 作品发布相关路由 ==========
    $router->post('/works/publish', 'PyApi\WorkPublishController@publishWork');
    $router->get('/works/my-works', 'PyApi\WorkPublishController@myWorks');
    $router->get('/works/gallery', 'PyApi\WorkPublishController@gallery');
    $router->get('/works/trending', 'PyApi\WorkPublishController@trending');
    $router->get('/works/{id}/share-link', 'PyApi\WorkPublishController@getShareLink');
    $router->post('/works/{id}/like', 'PyApi\WorkPublishController@like');
    $router->put('/works/{id}', 'PyApi\WorkPublishController@update');
    $router->delete('/works/{id}', 'PyApi\WorkPublishController@delete');

    // ========== 工作流管理相关路由 ==========
    $router->post('/workflows/create', 'PyApi\WorkflowController@create');
    $router->get('/workflows', 'PyApi\WorkflowController@index');
    $router->post('/workflows/{id}/execute', 'PyApi\WorkflowController@execute');
    $router->get('/workflows/{id}/execution-status', 'PyApi\WorkflowController@getExecutionStatus');
    $router->post('/workflows/{id}/step-input', 'PyApi\WorkflowController@provideStepInput');
    $router->post('/workflows/{id}/cancel', 'PyApi\WorkflowController@cancelExecution');
    $router->get('/workflows/{id}/history', 'PyApi\WorkflowController@getExecutionHistory');
    $router->get('/workflows/{id}', 'PyApi\WorkflowController@show');

    // ========== 用户成长相关路由 ==========
    $router->get('/user-growth/profile', 'PyApi\UserGrowthController@profile');
    $router->get('/user-growth/leaderboard', 'PyApi\UserGrowthController@leaderboard');
    $router->get('/user-growth/daily-tasks', 'PyApi\UserGrowthController@dailyTasks');
    $router->get('/user-growth/history', 'PyApi\UserGrowthController@history');
    $router->get('/user-growth/statistics', 'PyApi\UserGrowthController@statistics');
    $router->post('/user-growth/goals', 'PyApi\UserGrowthController@setGoals');
    $router->get('/user-growth/recommendations', 'PyApi\UserGrowthController@recommendations');
    $router->get('/user-growth/milestones', 'PyApi\UserGrowthController@milestones');
    $router->post('/user-growth/daily-tasks/{id}/complete', 'PyApi\UserGrowthController@completeDailyTask');
    $router->post('/user-growth/achievements/{id}/complete', 'PyApi\UserGrowthController@completeAchievement');

    // ========== 风格创建相关路由（认证后访问）==========
    $router->post('/styles/create', 'PyApi\StyleController@create');
});

// ==================== PY_API 阶段3：管理路由（需要管理员权限）====================
// 注意：认证和权限检查通过控制器内的方法处理，不使用中间件
$router->group(['prefix' => 'py-api'], function () use ($router) {

    // ========== 权限管理相关路由 ==========
    $router->post('/permissions/check', 'PyApi\PermissionController@checkPermission');
    $router->get('/permissions/roles', 'PyApi\PermissionController@getRoles');
    $router->put('/permissions/assign-role', 'PyApi\PermissionController@assignRole');
    $router->post('/permissions/grant', 'PyApi\PermissionController@grantPermission');
    $router->delete('/permissions/revoke', 'PyApi\PermissionController@revokePermission');
    $router->get('/permissions/history', 'PyApi\PermissionController@getPermissionHistory');
    $router->get('/permissions/user/{id}', 'PyApi\PermissionController@getUserPermissions');

    // ========== 系统分析相关路由 ==========
    $router->get('/analytics/user-behavior', 'PyApi\AnalyticsController@getUserBehaviorAnalytics');
    $router->get('/analytics/system-usage', 'PyApi\AnalyticsController@getSystemUsage');
    $router->get('/analytics/ai-performance', 'PyApi\AnalyticsController@getAiPerformanceAnalytics');
    $router->get('/analytics/user-retention', 'PyApi\AnalyticsController@getUserRetentionAnalytics');
    $router->get('/analytics/revenue', 'PyApi\AnalyticsController@getRevenueAnalytics');
    $router->post('/analytics/custom-report', 'PyApi\AnalyticsController@generateCustomReport');

    // ========== 广告管理相关路由 ==========
    $router->post('/ad.store', 'PyApi\AdController@ad_store');
    $router->post('/ad.update', 'PyApi\AdController@ad_update');
});

// ==================== PY_API 阶段4：特殊路由处理 ====================
// WebSocket认证路由（特殊处理）
$router->group(['prefix' => 'py-api'], function () use ($router) {
    $router->post('/websocket/auth', 'PyApi\WebSocketController@authenticate');
    $router->get('/websocket/sessions', 'PyApi\WebSocketController@getSessions');
    $router->delete('/websocket/disconnect', 'PyApi\WebSocketController@disconnect');
    $router->get('/websocket/status', 'PyApi\WebSocketController@getStatus');
});

// ==================== PY_API 阶段5：批量操作路由（统一管理） ====================
// 注意：认证通过控制器内的AuthService::authenticate()方法处理，不使用中间件
$router->group(['prefix' => 'py-api/batch'], function () use ($router) {
    $router->post('/images/generate', 'PyApi\BatchController@generateImages');
    $router->post('/voices/synthesize', 'PyApi\BatchController@synthesizeVoices');
    $router->post('/music/generate', 'PyApi\BatchController@generateMusic');
    $router->get('/tasks/status', 'PyApi\AiTaskController@batchStatus');
    $router->post('/tasks/cancel', 'PyApi\BatchController@cancelBatch');

    // 🚨 CogniDev新增：里程碑3接口1 - 批量资源生成任务
    $router->post('/resources/generate', 'PyApi\BatchController@generateResources');

    // 🚨 CogniDev新增：里程碑3接口2 - 批量任务状态查询
    $router->get('/resources/status', 'PyApi\BatchController@getResourcesStatus');
});

